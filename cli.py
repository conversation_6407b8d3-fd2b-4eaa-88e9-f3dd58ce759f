#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令行界面优化
提供更友好的CLI体验
"""

import argparse
import sys
import os
sys.path.append(os.path.dirname(__file__))
from config import Config
from logger_config import log

class CLI:
    """命令行界面类"""
    
    def __init__(self):
        self.parser = self._create_parser()
    
    def _create_parser(self):
        """创建命令行解析器"""
        parser = argparse.ArgumentParser(
            description="🔍 指纹识别 + Nuclei自动化扫描工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
示例用法:
  %(prog)s -u http://example.com                    # 扫描单个URL
  %(prog)s -f urls.txt                              # 扫描URL列表
  %(prog)s -u http://example.com --fp-only          # 仅指纹识别
  %(prog)s -f urls.txt --threads 20                 # 自定义线程数
  %(prog)s -u http://example.com --no-cache         # 禁用缓存
  %(prog)s --clear-cache                            # 清空缓存
  %(prog)s --stats                                  # 显示统计信息
            """
        )
        
        # 目标参数组
        target_group = parser.add_mutually_exclusive_group(required=False)
        target_group.add_argument(
            "-u", "--url", 
            help="目标URL"
        )
        target_group.add_argument(
            "-f", "--file", 
            help="URL列表文件"
        )
        
        # 扫描配置
        scan_group = parser.add_argument_group("扫描配置")
        scan_group.add_argument(
            "--fp-threads", 
            type=int, 
            default=Config.DEFAULT_FP_THREADS,
            help=f"指纹识别线程数 (默认: {Config.DEFAULT_FP_THREADS})"
        )
        scan_group.add_argument(
            "--scan-threads", 
            type=int, 
            default=Config.DEFAULT_SCAN_THREADS,
            help=f"Nuclei扫描线程数 (默认: {Config.DEFAULT_SCAN_THREADS})"
        )
        scan_group.add_argument(
            "--timeout", 
            type=int, 
            default=Config.HTTP_TIMEOUT,
            help=f"HTTP请求超时时间 (默认: {Config.HTTP_TIMEOUT}秒)"
        )
        
        # 功能选项
        feature_group = parser.add_argument_group("功能选项")
        feature_group.add_argument(
            "--fp-only", 
            action="store_true",
            help="仅执行指纹识别，不进行Nuclei扫描"
        )
        feature_group.add_argument(
            "--no-cache", 
            action="store_true",
            help="禁用指纹识别缓存"
        )
        feature_group.add_argument(
            "--clear-cache", 
            action="store_true",
            help="清空缓存并退出"
        )
        feature_group.add_argument(
            "--stats", 
            action="store_true",
            help="显示缓存统计信息并退出"
        )
        
        # 输出选项
        output_group = parser.add_argument_group("输出选项")
        output_group.add_argument(
            "-o", "--output", 
            help="指定输出目录 (默认: output/)"
        )
        output_group.add_argument(
            "--quiet", 
            action="store_true",
            help="静默模式，减少输出"
        )
        output_group.add_argument(
            "--verbose", 
            action="store_true",
            help="详细模式，显示更多信息"
        )
        
        return parser
    
    def parse_args(self):
        """解析命令行参数"""
        args = self.parser.parse_args()
        
        # 验证参数
        if not any([args.url, args.file, args.clear_cache, args.stats]):
            self.parser.error("必须指定 -u/--url 或 -f/--file 或 --clear-cache 或 --stats")
        
        return args
    
    def print_banner(self):
        """打印程序横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    🔍 Auto Nuclei Scanner                    ║
║                  指纹识别 + Nuclei自动化扫描                  ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def validate_file(self, file_path):
        """验证文件是否存在"""
        import os
        if not os.path.exists(file_path):
            log.error(f"文件不存在: {file_path}")
            sys.exit(1)
        return True
    
    def load_urls_from_file(self, file_path):
        """从文件加载URL列表"""
        self.validate_file(file_path)
        
        urls = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if not line.startswith(('http://', 'https://')):
                            log.warning(f"第{line_num}行URL格式可能不正确: {line}")
                        urls.append(line)
            
            if not urls:
                log.error(f"文件中没有找到有效的URL: {file_path}")
                sys.exit(1)
                
            log.info(f"从文件加载了 {len(urls)} 个URL", "CLI")
            return urls
            
        except Exception as e:
            log.error(f"读取文件失败: {e}")
            sys.exit(1)
