#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Spring Boot Actuator 专项扫描工具
针对 Spring Boot Actuator 未授权访问漏洞进行专项检测
"""

import requests
import json
import argparse
import sys
from urllib.parse import urljoin
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ActuatorScanner:
    def __init__(self, target_url):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.session.verify = False
        self.vulnerabilities = []
        
        # Spring Boot Actuator 常见端点
        self.endpoints = [
            '/actuator',
            '/actuator/env',
            '/actuator/health',
            '/actuator/info',
            '/actuator/beans',
            '/actuator/configprops',
            '/actuator/loggers',
            '/actuator/heapdump',
            '/actuator/threaddump',
            '/actuator/metrics',
            '/actuator/mappings',
            '/actuator/scheduledtasks',
            '/actuator/conditions',
            '/actuator/caches',
            '/actuator/jolokia',
            '/actuator/trace',
            '/actuator/dump',
            '/actuator/autoconfig',
            '/actuator/shutdown',
            '/actuator/restart',
            '/actuator/refresh',
            '/actuator/flyway',
            '/actuator/liquibase',
            '/actuator/prometheus',
            '/actuator/auditevents',
            '/actuator/httptrace',
            '/actuator/sessions',
            '/actuator/gateway',
            '/actuator/gateway/routes'
        ]

    def check_endpoint(self, endpoint):
        """检查单个端点"""
        url = urljoin(self.target_url, endpoint)
        try:
            response = self.session.get(url, timeout=10)
            return response
        except Exception as e:
            print(f"[!] 请求失败 {url}: {e}")
            return None

    def analyze_response(self, endpoint, response):
        """分析响应内容"""
        if response is None:
            return
            
        url = urljoin(self.target_url, endpoint)
        
        # 检查状态码
        if response.status_code == 200:
            content_type = response.headers.get('Content-Type', '').lower()
            content = response.text
            
            # 分析不同端点的风险等级
            risk_level = self.assess_risk(endpoint, content, response)
            
            vuln = {
                'endpoint': endpoint,
                'url': url,
                'status_code': response.status_code,
                'content_type': content_type,
                'risk_level': risk_level,
                'content_length': len(content),
                'description': self.get_endpoint_description(endpoint)
            }
            
            self.vulnerabilities.append(vuln)
            
            # 输出发现的端点
            risk_color = self.get_risk_color(risk_level)
            print(f"[{risk_color}] {endpoint} - {risk_level} - {vuln['description']}")
            
            # 对于特别敏感的端点，显示部分内容
            if risk_level in ['CRITICAL', 'HIGH'] and len(content) > 0:
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"    内容预览: {preview}")

    def assess_risk(self, endpoint, content, response):
        """评估端点风险等级"""
        if endpoint in ['/actuator/heapdump', '/actuator/threaddump']:
            return 'CRITICAL'
        elif endpoint in ['/actuator/env', '/actuator/configprops', '/actuator/beans']:
            if 'password' in content.lower() or 'secret' in content.lower():
                return 'CRITICAL'
            return 'HIGH'
        elif endpoint in ['/actuator/shutdown', '/actuator/restart']:
            return 'CRITICAL'
        elif endpoint in ['/actuator/loggers', '/actuator/mappings', '/actuator/metrics']:
            return 'MEDIUM'
        elif endpoint in ['/actuator/health', '/actuator/info']:
            return 'LOW'
        else:
            return 'MEDIUM'

    def get_endpoint_description(self, endpoint):
        """获取端点描述"""
        descriptions = {
            '/actuator': 'Actuator 主页面，列出所有可用端点',
            '/actuator/env': '环境变量信息，可能包含敏感配置',
            '/actuator/health': '应用健康状态',
            '/actuator/info': '应用基本信息',
            '/actuator/beans': 'Spring Bean 信息',
            '/actuator/configprops': '配置属性信息',
            '/actuator/loggers': '日志配置信息',
            '/actuator/heapdump': '堆转储文件下载（极危险）',
            '/actuator/threaddump': '线程转储信息',
            '/actuator/metrics': '应用指标信息',
            '/actuator/mappings': 'URL映射信息',
            '/actuator/scheduledtasks': '定时任务信息',
            '/actuator/shutdown': '关闭应用端点（极危险）',
            '/actuator/restart': '重启应用端点（极危险）'
        }
        return descriptions.get(endpoint, '未知端点')

    def get_risk_color(self, risk_level):
        """获取风险等级对应的颜色标识"""
        colors = {
            'CRITICAL': '🔴',
            'HIGH': '🟠', 
            'MEDIUM': '🟡',
            'LOW': '🟢'
        }
        return colors.get(risk_level, '⚪')

    def scan(self):
        """执行扫描"""
        print(f"[*] 开始扫描 Spring Boot Actuator: {self.target_url}")
        print(f"[*] 检查 {len(self.endpoints)} 个常见端点...")
        print("-" * 80)
        
        for endpoint in self.endpoints:
            response = self.check_endpoint(endpoint)
            self.analyze_response(endpoint, response)
        
        print("-" * 80)
        self.generate_report()

    def generate_report(self):
        """生成扫描报告"""
        if not self.vulnerabilities:
            print("[✓] 未发现 Actuator 端点暴露")
            return
        
        print(f"[!] 发现 {len(self.vulnerabilities)} 个暴露的 Actuator 端点:")
        
        # 按风险等级分组
        risk_groups = {}
        for vuln in self.vulnerabilities:
            risk = vuln['risk_level']
            if risk not in risk_groups:
                risk_groups[risk] = []
            risk_groups[risk].append(vuln)
        
        # 输出统计信息
        for risk in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
            if risk in risk_groups:
                count = len(risk_groups[risk])
                color = self.get_risk_color(risk)
                print(f"  {color} {risk}: {count} 个端点")
        
        # 保存详细报告
        self.save_report()

    def save_report(self):
        """保存详细报告到文件"""
        report_file = f"actuator_scan_report.json"
        report_data = {
            'target': self.target_url,
            'total_endpoints_checked': len(self.endpoints),
            'vulnerable_endpoints': len(self.vulnerabilities),
            'vulnerabilities': self.vulnerabilities
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"[✓] 详细报告已保存到: {report_file}")

def main():
    parser = argparse.ArgumentParser(description="Spring Boot Actuator 专项扫描工具")
    parser.add_argument("-u", "--url", required=True, help="目标URL")
    args = parser.parse_args()
    
    scanner = ActuatorScanner(args.url)
    scanner.scan()

if __name__ == "__main__":
    main()
