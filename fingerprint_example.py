#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指纹识别类使用示例
演示如何单独使用 FingerprintScanner 类
"""

from fnuclei import FingerprintScanner
from script.logger_config import log

def main():
    # 创建指纹扫描器实例
    scanner = FingerprintScanner()
    
    # 测试URL列表
    test_urls = [
        "http://httpbin.org/get",
        "https://www.baidu.com",
        "https://github.com"
    ]
    
    log.info("开始指纹识别测试", "TEST")
    
    # 方法1: 单个URL扫描
    log.info("=== 单个URL扫描 ===", "TEST")
    for url in test_urls:
        result = scanner.scan_url(url)
        print(f"URL: {result['url']}")
        print(f"CMS: {result['cms']}")
        print("-" * 50)
    
    # 方法2: 批量URL扫描
    log.info("=== 批量URL扫描 ===", "TEST")
    result_file = scanner.scan_urls(test_urls, threads=3, output='temp/test_results.json')
    log.success(f"批量扫描完成，结果保存到: {result_file}", "TEST")

if __name__ == "__main__":
    main()
