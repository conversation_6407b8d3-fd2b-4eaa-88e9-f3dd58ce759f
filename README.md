# Auto Nuclei - 自动化漏洞扫描工具

## 项目简介

Auto Nuclei 是一个基于指纹识别的自动化漏洞扫描工具，能够：

1. **智能指纹识别** - 识别目标网站的技术栈和CMS
2. **精准漏洞扫描** - 根据识别结果进行针对性的Nuclei扫描
3. **多线程处理** - 支持并发扫描，提高效率
4. **详细报告** - 生成详细的扫描报告

## 目录结构

```
auto_nuclei/
├── src/                    # 源代码
│   ├── fnuclei.py         # 主程序
│   ├── actuator_scan.py   # Spring Boot Actuator专项扫描
│   └── fingerprint.py     # 指纹识别模块
├── config/                 # 配置文件
│   └── config.ini         # 主配置文件
├── data/                   # 数据文件
│   ├── fingerprints/      # 指纹数据
│   └── tags/              # 标签数据
├── results/               # 扫描结果
├── logs/                  # 日志文件
├── scripts/               # 辅助脚本
├── docs/                  # 文档
└── requirements.txt       # 依赖文件
```

## 安装使用

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **基本使用**
   ```bash
   # 扫描单个URL
   python src/fnuclei.py -u http://example.com
   
   # 扫描URL列表
   python src/fnuclei.py -f urls.txt
   
   # Spring Boot Actuator专项扫描
   python src/actuator_scan.py -u http://example.com
   ```

3. **参数说明**
   - `-u, --url`: 目标URL
   - `-f, --file`: URL列表文件
   - `--fp-threads`: 指纹识别线程数 (默认: 10)
   - `--scan-threads`: Nuclei扫描线程数 (默认: 5)

## 功能特性

- ✅ 支持785+种CMS和技术栈指纹识别
- ✅ 智能标签映射，精准漏洞扫描
- ✅ 多线程并发处理
- ✅ 详细的日志记录
- ✅ Spring Boot Actuator专项检测
- ✅ Linux系统完全兼容

## 更新日志

- v2.0: 重构项目结构，优化代码组织
- v1.5: 添加Spring Boot Actuator专项扫描
- v1.0: 基础功能实现
