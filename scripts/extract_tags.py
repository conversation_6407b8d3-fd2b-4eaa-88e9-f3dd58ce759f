#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从 finger.json 提取标签并生成 nuclei 兼容的标签文件
"""

import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_tags_from_fingerprints():
    """从指纹文件中提取所有CMS标签"""
    try:
        with open('finger.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        fingerprints = data.get('fingerprint', [])
        cms_tags = set()
        
        for fp in fingerprints:
            cms = fp.get('cms', '').strip()
            if cms:
                # 清理和标准化标签名
                cms_clean = clean_tag_name(cms)
                if cms_clean:
                    cms_tags.add(cms_clean)
        
        logger.info(f"从指纹文件中提取到 {len(cms_tags)} 个唯一标签")
        return sorted(list(cms_tags))
    
    except Exception as e:
        logger.error(f"提取标签失败: {e}")
        return []

def clean_tag_name(cms_name):
    """清理和标准化标签名"""
    if not cms_name:
        return ""
    
    # 标签映射规则
    tag_mapping = {
        # OA系统
        '致远OA': 'seeyon',
        '致远OA M1 Server': 'seeyon',
        '致远OA M3 Server': 'seeyon',
        '通达OA': 'tongda',
        '泛微 OA': 'weaver',
        '泛微云桥 e-Bridge': 'weaver',
        '蓝凌 OA': 'landray',
        '九思 OA 协同办公系统': 'jiusi',
        '红帆-ioffice OA': 'ioffice',
        '微宏 OA': 'weihong',
        '顶讯科技-易宝OA系统': 'yibao',
        
        # Web服务器
        'Weblogic': 'weblogic',
        'Apache Tomcat': 'tomcat',
        'Tomcat默认页面': 'tomcat',
        'Jboss': 'jboss',
        'JBoss Application Server 7': 'jboss',
        
        # CMS系统
        'Drupal': 'drupal',
        'Joomla': 'joomla',
        'WordPress': 'wordpress',
        'Discuz!': 'discuz',
        'PbootCMS': 'pbootcms',
        'TWCMS': 'twcms',
        'DouPHP': 'douphp',
        'Typecho': 'typecho',
        'Dreamer CMS': 'dreamer',
        'Ghost (CMS)': 'ghost',
        
        # 框架和环境
        'Spring env': 'spring',
        'spring-boot': 'spring-boot',
        'Shiro': 'shiro',
        'Swagger UI': 'swagger',
        
        # 数据库和中间件
        'phpMyAdmin': 'phpmyadmin',
        'RabbitMQ': 'rabbitmq',
        'Zabbix': 'zabbix',
        'Kibana': 'kibana',
        'Jenkins': 'jenkins',
        'Gitlab': 'gitlab',
        'Docker': 'docker',
        
        # 网络设备
        'Sangfor SSL VPN': 'sangfor',
        'Cisco SSLVPN': 'cisco',
        'pfSense': 'pfsense',
        'Ubiquiti': 'ubiquiti',
        'TP-LINK': 'tplink',
        'D-Link': 'dlink',
        'Netgear': 'netgear',
        'Huawei': 'huawei',
        'H3C Router': 'h3c',
        
        # 安全设备
        '深信服 waf': 'sangfor',
        '深信服防火墙数据中心': 'sangfor',
        '深信服一体化网关 MIG': 'sangfor',
        '深信服上网行为管理系统': 'sangfor',
        '深信服应用交付报表系统': 'sangfor',
        '天融信防火墙': 'topsec',
        '天融信TopAPP负载均衡系统': 'topsec',
        '天融信VPN设备': 'topsec',
        '天玥运维安全网关': 'tianyue',
        '启明星辰天清汉马USG防火墙': 'venustech',
        '网御 vpn': 'leadsec',
        'Palo Alto Networks': 'paloalto',
        'Palo Alto Login Portal': 'paloalto',
        'CheckPoint': 'checkpoint',
        'SonicWALL': 'sonicwall',
        'Dell SonicWALL': 'sonicwall',
        'Fortinet': 'fortinet',
        'Sophos': 'sophos',
        
        # 邮件系统
        'CoreMail': 'coremail',
        'Outlook Web Application': 'owa',
        'Microsoft OWA': 'owa',
        'Roundcube Webmail': 'roundcube',
        'Zimbra': 'zimbra',
        
        # 虚拟化
        'VMware Horizon': 'vmware',
        'Vmware Secure File Transfer': 'vmware',
        'Citrix': 'citrix',
        
        # 云平台
        'Amazon': 'aws',
        'OpenStack': 'openstack',
        
        # 面板系统
        '宝塔-BT.cn': 'bt',
        'cPanel Login': 'cpanel',
        'Plesk': 'plesk',
        'Webmin': 'webmin',
        'WHM': 'whm',
        
        # 监控系统
        'Prometheus Time Series Collection and Processing Server': 'prometheus',
        'Grafana': 'grafana',
        'NetData': 'netdata',
        'PRTG Network Monitor': 'prtg',
        
        # 其他
        '列目录': 'directory-listing',
        '401 登陆认证': 'http-auth',
        'Influxdb': 'influxdb',
        'Elastic (Database)': 'elasticsearch',
    }
    
    # 直接映射
    if cms_name in tag_mapping:
        return tag_mapping[cms_name]
    
    # 模糊匹配和清理
    cms_lower = cms_name.lower()
    
    # 移除常见的后缀和前缀
    cms_clean = cms_lower
    for remove_word in ['系统', '平台', '管理', '软件', '服务', '设备', '网关', '防火墙', 
                       '服务器', '应用', '工具', '客户端', '控制台', '面板', '中心']:
        cms_clean = cms_clean.replace(remove_word, '')
    
    # 移除特殊字符，只保留字母数字和连字符
    import re
    cms_clean = re.sub(r'[^\w\-]', '', cms_clean)
    cms_clean = re.sub(r'[-_]+', '-', cms_clean)
    cms_clean = cms_clean.strip('-_')
    
    # 如果清理后为空或太短，返回原始名称的简化版本
    if len(cms_clean) < 2:
        cms_clean = re.sub(r'[^\w]', '', cms_lower)[:20]
    
    return cms_clean if cms_clean else None

def create_nuclei_tags_file(tags):
    """创建Nuclei兼容的标签文件"""
    nuclei_tags = {
        "tags": [{"name": tag} for tag in tags]
    }
    
    with open('nuclei-tags.json', 'w', encoding='utf-8') as f:
        json.dump(nuclei_tags, f, ensure_ascii=False, indent=2)
    
    logger.info(f"已创建 nuclei-tags.json 文件，包含 {len(tags)} 个标签")

def main():
    logger.info("开始从指纹文件提取标签...")
    
    # 提取标签
    tags = extract_tags_from_fingerprints()
    
    if not tags:
        logger.error("未能提取到任何标签")
        return
    
    # 创建标签文件
    create_nuclei_tags_file(tags)
    
    # 显示提取的标签
    logger.info("提取的标签列表:")
    for i, tag in enumerate(tags, 1):
        print(f"{i:3d}. {tag}")
    
    logger.info("标签提取完成！")

if __name__ == "__main__":
    main()
