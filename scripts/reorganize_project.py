#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新规划项目目录结构，清理不必要的文件
"""

import os
import shutil
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_directory_structure():
    """创建新的目录结构"""
    directories = [
        'src',                    # 源代码目录
        'config',                 # 配置文件目录
        'data',                   # 数据文件目录
        'data/fingerprints',      # 指纹数据
        'data/tags',              # 标签数据
        'results',                # 扫描结果目录
        'logs',                   # 日志目录
        'docs',                   # 文档目录
        'scripts',                # 辅助脚本目录
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"创建目录: {directory}")

def move_files():
    """移动文件到新的目录结构"""
    file_moves = [
        # 源代码文件
        ('fnuclei.py', 'src/fnuclei.py'),
        ('actuator_scan.py', 'src/actuator_scan.py'),
        ('fingerprint.py', 'src/fingerprint.py'),
        
        # 配置和数据文件
        ('finger.json', 'data/fingerprints/finger.json'),
        ('nuclei-tags.json', 'data/tags/nuclei-tags.json'),
        
        # 辅助脚本
        ('extract_tags.py', 'scripts/extract_tags.py'),
        ('reorganize_project.py', 'scripts/reorganize_project.py'),
        
        # 文档
        ('README.md', 'docs/README.md'),
    ]
    
    for src, dst in file_moves:
        if os.path.exists(src):
            shutil.move(src, dst)
            logger.info(f"移动文件: {src} -> {dst}")
        else:
            logger.warning(f"文件不存在: {src}")

def move_results():
    """移动结果文件"""
    if os.path.exists('result'):
        if os.path.exists('results'):
            shutil.rmtree('results')
        shutil.move('result', 'results')
        logger.info("移动结果目录: result -> results")

def cleanup_files():
    """清理不必要的文件"""
    files_to_remove = [
        'simple-tags.json',           # 已被 nuclei-tags.json 替代
        'res.json',                   # 临时结果文件
        'havetag.txt',               # 临时文件
        'notag.txt',                 # 临时文件
        'fnuclei.log',               # 旧日志文件
        'TEMPLATES-STATS.json',      # 不需要的模板文件
        'actuator_scan_report.json', # 临时报告文件
    ]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"删除文件: {file_path}")

def cleanup_directories():
    """清理不必要的目录"""
    dirs_to_remove = [
        '__pycache__',
    ]
    
    for dir_path in dirs_to_remove:
        if os.path.exists(dir_path):
            shutil.rmtree(dir_path)
            logger.info(f"删除目录: {dir_path}")

def create_config_files():
    """创建配置文件"""
    
    # 创建主配置文件
    config_content = """# Auto Nuclei 配置文件

[fingerprint]
# 指纹识别配置
fingerprint_file = data/fingerprints/finger.json
threads = 10
timeout = 10

[nuclei]
# Nuclei 扫描配置
tags_file = data/tags/nuclei-tags.json
threads = 5
timeout = 300
severity = critical,high,medium

[output]
# 输出配置
results_dir = results
logs_dir = logs
format = json

[network]
# 网络配置
user_agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
verify_ssl = false
"""
    
    with open('config/config.ini', 'w', encoding='utf-8') as f:
        f.write(config_content)
    logger.info("创建配置文件: config/config.ini")

def create_requirements():
    """创建依赖文件"""
    requirements = """requests>=2.28.0
beautifulsoup4>=4.11.0
argparse
urllib3
"""
    
    with open('requirements.txt', 'w', encoding='utf-8') as f:
        f.write(requirements)
    logger.info("创建依赖文件: requirements.txt")

def create_gitignore():
    """创建 .gitignore 文件"""
    gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 日志文件
*.log
logs/

# 扫描结果
results/
*.json
*.txt

# 临时文件
temp/
tmp/

# IDE
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db
"""
    
    with open('.gitignore', 'w', encoding='utf-8') as f:
        f.write(gitignore_content)
    logger.info("创建 .gitignore 文件")

def create_readme():
    """创建新的 README 文件"""
    readme_content = """# Auto Nuclei - 自动化漏洞扫描工具

## 项目简介

Auto Nuclei 是一个基于指纹识别的自动化漏洞扫描工具，能够：

1. **智能指纹识别** - 识别目标网站的技术栈和CMS
2. **精准漏洞扫描** - 根据识别结果进行针对性的Nuclei扫描
3. **多线程处理** - 支持并发扫描，提高效率
4. **详细报告** - 生成详细的扫描报告

## 目录结构

```
auto_nuclei/
├── src/                    # 源代码
│   ├── fnuclei.py         # 主程序
│   ├── actuator_scan.py   # Spring Boot Actuator专项扫描
│   └── fingerprint.py     # 指纹识别模块
├── config/                 # 配置文件
│   └── config.ini         # 主配置文件
├── data/                   # 数据文件
│   ├── fingerprints/      # 指纹数据
│   └── tags/              # 标签数据
├── results/               # 扫描结果
├── logs/                  # 日志文件
├── scripts/               # 辅助脚本
├── docs/                  # 文档
└── requirements.txt       # 依赖文件
```

## 安装使用

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **基本使用**
   ```bash
   # 扫描单个URL
   python src/fnuclei.py -u http://example.com
   
   # 扫描URL列表
   python src/fnuclei.py -f urls.txt
   
   # Spring Boot Actuator专项扫描
   python src/actuator_scan.py -u http://example.com
   ```

3. **参数说明**
   - `-u, --url`: 目标URL
   - `-f, --file`: URL列表文件
   - `--fp-threads`: 指纹识别线程数 (默认: 10)
   - `--scan-threads`: Nuclei扫描线程数 (默认: 5)

## 功能特性

- ✅ 支持785+种CMS和技术栈指纹识别
- ✅ 智能标签映射，精准漏洞扫描
- ✅ 多线程并发处理
- ✅ 详细的日志记录
- ✅ Spring Boot Actuator专项检测
- ✅ Linux系统完全兼容

## 更新日志

- v2.0: 重构项目结构，优化代码组织
- v1.5: 添加Spring Boot Actuator专项扫描
- v1.0: 基础功能实现
"""
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    logger.info("创建 README.md 文件")

def main():
    """主函数"""
    logger.info("开始重新规划项目目录结构...")
    
    # 1. 创建目录结构
    create_directory_structure()
    
    # 2. 移动文件
    move_files()
    move_results()
    
    # 3. 清理不必要的文件和目录
    cleanup_files()
    cleanup_directories()
    
    # 4. 创建配置文件
    create_config_files()
    
    # 5. 创建项目文件
    create_requirements()
    create_gitignore()
    create_readme()
    
    logger.info("项目目录重新规划完成！")
    
    # 显示新的目录结构
    logger.info("\n新的项目结构:")
    for root, dirs, files in os.walk('.'):
        level = root.replace('.', '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            if not file.startswith('.'):
                print(f"{subindent}{file}")

if __name__ == "__main__":
    main()
