import os
import sys
from datetime import datetime
from loguru import logger
from typing import Dict, Any, Optional, Union

"""
日志工具

功能描述:
1. 统一的日志配置和管理
2. 支持控制台和文件输出
3. 支持日志轮转和保留策略
4. 支持JSON格式和文本格式
5. 支持消息标签和分类
"""

class LogConfig:
    """增强的日志配置类"""
    
    @staticmethod
    def setup_logging(
        app_name: str = "app",
        log_dir: Optional[str] = None,
        console_level: str = "INFO",
        file_level: str = "DEBUG",
        json_format: bool = False,
        retention: str = "30 days",
        compression: str = "zip",
        rotation: Union[str, int] = "00:00"
    ):
        """配置日志设置
        
        Args:
            app_name: 应用名称，用于日志文件名前缀
            log_dir: 日志目录，默认为项目根目录下的logs
            console_level: 控制台日志级别
            file_level: 文件日志级别
            json_format: 是否使用JSON格式输出
            retention: 日志保留时间
            compression: 日志压缩方式
            rotation: 日志轮转策略，可以是时间("00:00")或大小("100 MB")
        """
        # 创建日志目录
        if log_dir is None:
            log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # 生成日志文件名（按日期和应用名称）
        log_file = os.path.join(
            log_dir, 
            f"{app_name}_{datetime.now().strftime('%Y-%m-%d')}.log"
        )
        
        # 移除默认的处理器
        logger.remove()
        
        # 配置日志格式
        if json_format:
            # JSON格式
            log_format = (
                '{"time": "{time:YYYY-MM-DD HH:mm:ss}", '
                '"level": "{level}", '
                '"message": "{message}", '
                '"module": "{name}", '
                '"function": "{function}", '
                '"line": "{line}"}'
            )
        else:
            # 文本格式
            log_format = (
                "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                "<level>{level: <8}</level> | "
                "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                "<level>{message}</level>"
            )
        
        # 添加控制台输出
        logger.add(
            sys.stderr,
            format=log_format,
            level=console_level,
            enqueue=True,
            colorize=not json_format
        )
        
        # 添加文件输出
        logger.add(
            log_file,
            format=log_format,
            level=file_level,
            rotation=rotation,  # 每天午夜轮换或按大小轮换
            retention=retention,  # 保留30天的日志
            compression=compression,  # 压缩旧日志
            encoding="utf-8",
            enqueue=True
        )
        
        logger.info(f"日志系统初始化完成，日志文件: {log_file}")
        return logger

    @staticmethod
    def log_message(message: str, level: str = "INFO", tag: Optional[str] = None, extra: Optional[Dict[str, Any]] = None):
        """记录消息内容
        
        Args:
            message: 消息内容
            level: 日志级别
            tag: 消息标签，用于分类
            extra: 额外的结构化数据
        """
        if extra is None:
            extra = {}
        
        if tag:
            extra["tag"] = tag
            formatted_message = f"[{tag}]\n{message}"
        else:
            formatted_message = message
        
        log_func = getattr(logger, level.lower())
        log_func(formatted_message, **extra)
    
    @staticmethod
    def get_logger():
        """获取配置好的logger实例"""
        return logger

class LogHandler:
    def __init__(self, app_name):
        """
        初始化日志处理器
        
        Args:
            app_name: 应用名称，用于日志文件名前缀
        """
        self.app_name = app_name
        # 获取项目根目录
        self.root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.log_dir = os.path.join(self.root_dir, 'logs')
        self._setup_logger()

    def _setup_logger(self):
        """配置日志设置"""
        # 创建日志目录
        if not os.path.exists(self.log_dir):
            try:
                os.makedirs(self.log_dir)
                print(f"Created log directory: {self.log_dir}")
            except Exception as e:
                print(f"Failed to create log directory: {e}")
                return

        # 生成日志文件名
        log_file = os.path.join(
            self.log_dir, 
            f"{self.app_name}_{datetime.now().strftime('%Y-%m-%d')}.log"
        )

        # 移除所有现有的处理程序
        logger.remove()

        # 添加控制台输出
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level="INFO"
        )

        # 添加文件输出
        try:
            logger.add(
                log_file,
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
                rotation="00:00",  # 每天轮换
                retention="30 days",  # 保留30天
                encoding="utf-8",
                level="INFO"
            )
            print(f"Logger initialized. Log file: {log_file}")
        except Exception as e:
            print(f"Failed to initialize logger: {e}")

    def get_logger(self):
        """返回配置好的logger"""
        return logger
