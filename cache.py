#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存模块
提供指纹识别结果缓存功能
"""

import json
import os
import sys
import hashlib
from datetime import datetime, timedelta
sys.path.append(os.path.dirname(__file__))
from logger_config import log

class FingerprintCache:
    """指纹识别缓存类"""
    
    def __init__(self, cache_file='temp/fingerprint_cache.json', expire_hours=24):
        self.cache_file = cache_file
        self.expire_hours = expire_hours
        self.cache_data = self._load_cache()
    
    def _load_cache(self):
        """加载缓存数据"""
        if not os.path.exists(self.cache_file):
            return {}
        
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 清理过期缓存
            self._clean_expired_cache(cache_data)
            return cache_data
        except:
            log.warning("缓存文件损坏，重新创建", "CACHE")
            return {}
    
    def _save_cache(self):
        """保存缓存数据"""
        os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)
        with open(self.cache_file, 'w', encoding='utf-8') as f:
            json.dump(self.cache_data, f, ensure_ascii=False, indent=2)
    
    def _clean_expired_cache(self, cache_data):
        """清理过期缓存"""
        current_time = datetime.now()
        expired_keys = []
        
        for url_hash, data in cache_data.items():
            cache_time = datetime.fromisoformat(data['timestamp'])
            if current_time - cache_time > timedelta(hours=self.expire_hours):
                expired_keys.append(url_hash)
        
        for key in expired_keys:
            del cache_data[key]
        
        if expired_keys:
            log.info(f"清理了 {len(expired_keys)} 个过期缓存", "CACHE")
    
    def _get_url_hash(self, url):
        """获取URL的哈希值"""
        return hashlib.md5(url.encode()).hexdigest()
    
    def get(self, url):
        """获取缓存结果"""
        url_hash = self._get_url_hash(url)
        if url_hash in self.cache_data:
            log.info(f"使用缓存结果: {url}", "CACHE")
            return self.cache_data[url_hash]['result']
        return None
    
    def set(self, url, result):
        """设置缓存结果"""
        url_hash = self._get_url_hash(url)
        self.cache_data[url_hash] = {
            'url': url,
            'result': result,
            'timestamp': datetime.now().isoformat()
        }
        self._save_cache()
    
    def clear(self):
        """清空缓存"""
        self.cache_data = {}
        if os.path.exists(self.cache_file):
            os.remove(self.cache_file)
        log.info("缓存已清空", "CACHE")
    
    def get_stats(self):
        """获取缓存统计"""
        return {
            'total_entries': len(self.cache_data),
            'cache_file': self.cache_file,
            'expire_hours': self.expire_hours
        }

# 全局缓存实例
fingerprint_cache = FingerprintCache()
