# Auto Nuclei - 项目结构说明

## 📁 目录结构

```
auto_nuclei/
├── fnuclei.py              # 主程序 (保持在根目录)
├── logger_config.py        # 日志配置模块
├── data/                   # 数据文件目录
│   ├── finger.json         # 指纹识别数据 (958个指纹)
│   └── nuclei-tags.json    # Nuclei标签数据 (785个标签)
├── output/                 # 扫描结果输出目录
├── temp/                   # 临时文件目录
│   └── res.json           # 指纹识别结果 (临时)
└── logs/                   # 日志文件目录
    └── fnuclei_YYYYMMDD.log
```

## 🎯 主要改进

### 1. 简洁的目录结构
- ✅ **fnuclei.py** 保持在主目录
- ✅ **data/** 存放所有数据文件
- ✅ **output/** 存放扫描结果
- ✅ **temp/** 存放临时文件
- ✅ **logs/** 存放日志文件

### 2. 结果文件命名规则
- ✅ **格式**: `{tag}_{YYYYMMDD}.txt`
- ✅ **示例**: `wordpress_20250607.txt`
- ✅ **位置**: `output/` 目录

### 3. 增强的日志输出
- ✅ **脚本名**: 显示调用的脚本名称
- ✅ **模块名**: 显示具体的函数/方法名
- ✅ **格式**: `时间 | 脚本:模块 | 级别 | 消息`

## 📊 日志输出示例

```
09:56:29 | fnuclei:main | INFO     | [FINGERPRINT] 正在执行指纹识别...
09:56:31 | fnuclei:match_fingerprint | SUCCESS  | 🔍 指纹识别: http://example.com -> wordpress
09:56:31 | fnuclei:main | INFO     | [NUCLEI] 指纹识别完成，开始 Nuclei 扫描...
09:56:31 | fnuclei:classify_targets | SUCCESS  | [MATCH] 找到 1/1 个匹配标签的目标
09:56:32 | fnuclei:scan_worker | INFO     | 🚀 扫描启动: http://example.com -> wordpress
09:56:45 | fnuclei:scan_worker | SUCCESS  | 🎯 发现漏洞: output/wordpress_20250607.txt
09:56:45 | fnuclei:start_scan_threads | SUCCESS  | [COMPLETE] 所有扫描任务完成！
```

## 🚀 使用方法

```bash
# 扫描单个URL
python3 fnuclei.py -u http://example.com

# 扫描URL列表
python3 fnuclei.py -f urls.txt

# 自定义线程数
python3 fnuclei.py -u http://example.com --fp-threads 20 --scan-threads 10
```

## 📋 文件说明

| 文件/目录 | 说明 |
|-----------|------|
| `fnuclei.py` | 主程序，包含指纹识别和Nuclei扫描逻辑 |
| `logger_config.py` | 日志配置模块，提供彩色日志输出 |
| `data/finger.json` | 指纹识别规则数据 |
| `data/nuclei-tags.json` | Nuclei标签映射数据 |
| `output/` | 扫描结果保存目录 |
| `temp/` | 临时文件目录 |
| `logs/` | 日志文件目录 |

## ⚡ 核心特性

1. **精准扫描** - 只扫描匹配到标签的目标
2. **简洁结构** - 最小化目录层级
3. **清晰日志** - 包含脚本名和模块信息
4. **规范命名** - 结果文件使用日期格式
5. **自动清理** - 临时文件自动删除

## 🔧 依赖要求

- Python 3.6+
- loguru
- requests
- beautifulsoup4
- nuclei (已安装并在PATH中)
