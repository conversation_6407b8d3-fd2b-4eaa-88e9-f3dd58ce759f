#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版本的 fnuclei 主程序
集成了配置管理、异常处理、缓存、统计等优化功能
"""

import sys
import time
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from config import Config
from logger_config import log
from fnuclei import FingerprintScanner, AutoNuclei

# 简化版本，暂时不使用复杂的CLI和缓存
import argparse

class OptimizedFnuclei:
    """优化版本的主程序类"""
    
    def __init__(self):
        self.cli = CLI()
        self.scanner = None
        
    def setup(self):
        """初始化设置"""
        # 确保目录存在
        Config.ensure_dirs()
        
        # 打印横幅
        self.cli.print_banner()
        
        # 解析命令行参数
        self.args = self.cli.parse_args()
        
        # 处理特殊命令
        if self.args.clear_cache:
            fingerprint_cache.clear()
            log.success("缓存已清空", "MAIN")
            sys.exit(0)
            
        if self.args.stats:
            self._show_cache_stats()
            sys.exit(0)
    
    def _show_cache_stats(self):
        """显示缓存统计"""
        cache_stats = fingerprint_cache.get_stats()
        log.info("缓存统计信息:", "STATS")
        log.info(f"  缓存条目: {cache_stats['total_entries']}", "STATS")
        log.info(f"  缓存文件: {cache_stats['cache_file']}", "STATS")
        log.info(f"  过期时间: {cache_stats['expire_hours']} 小时", "STATS")
    
    def get_urls(self):
        """获取要扫描的URL列表"""
        if self.args.url:
            return [self.args.url]
        elif self.args.file:
            return self.cli.load_urls_from_file(self.args.file)
        else:
            log.error("未指定扫描目标")
            sys.exit(1)
    
    def run_fingerprint_scan(self, urls):
        """执行指纹识别扫描"""
        log.info("正在执行指纹识别...", "FINGERPRINT")
        
        # 创建扫描器
        try:
            self.scanner = FingerprintScanner(Config.FINGERPRINT_FILE)
        except FingerprintFileNotFound as e:
            log.error(str(e))
            sys.exit(1)
        
        # 开始计时
        start_time = time.time()
        
        # 执行扫描（集成缓存）
        results = []
        for url in urls:
            if not self.args.no_cache:
                cached_result = fingerprint_cache.get(url)
                if cached_result:
                    results.append(cached_result)
                    stats.add_fingerprint_result(cached_result)
                    continue
            
            # 执行扫描
            result = self.scanner.scan_url(url)
            results.append(result)
            stats.add_fingerprint_result(result)
            
            # 保存到缓存
            if not self.args.no_cache:
                fingerprint_cache.set(url, result)
        
        # 记录耗时
        fingerprint_time = time.time() - start_time
        stats.set_fingerprint_time(fingerprint_time)
        
        # 保存结果
        output_file = f"{Config.TEMP_DIR}/res.json"
        import json
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        return output_file
    
    def run_nuclei_scan(self, res_file):
        """执行Nuclei扫描"""
        if self.args.fp_only:
            log.info("仅执行指纹识别，跳过Nuclei扫描", "NUCLEI")
            return
        
        log.info("指纹识别完成，开始 Nuclei 扫描...", "NUCLEI")
        
        # 开始计时
        start_time = time.time()
        
        try:
            # 执行Nuclei扫描
            nuclei_scanner = AutoNuclei(
                res_file=res_file, 
                thread_count=self.args.scan_threads
            )
            
            # 记录统计信息
            total_targets = sum(len(urls) for urls in nuclei_scanner.cms_targets.values())
            matched_targets = sum(len(urls) for urls in nuclei_scanner.tagged_targets.values())
            stats.set_nuclei_targets(total_targets, matched_targets)
            
        except TagsFileNotFound as e:
            log.error(str(e))
            sys.exit(1)
        except Exception as e:
            log.error(f"Nuclei扫描失败: {e}")
            sys.exit(1)
        
        # 记录耗时
        nuclei_time = time.time() - start_time
        stats.set_nuclei_time(nuclei_time)
    
    def run(self):
        """主运行函数"""
        try:
            # 初始化
            self.setup()
            
            # 开始统计
            stats.start_scan()
            
            # 获取URL列表
            urls = self.get_urls()
            log.info(f"准备扫描 {len(urls)} 个目标", "MAIN")
            
            # 执行指纹识别
            res_file = self.run_fingerprint_scan(urls)
            
            # 执行Nuclei扫描
            self.run_nuclei_scan(res_file)
            
            # 结束统计
            stats.end_scan()
            
        except KeyboardInterrupt:
            log.warning("用户中断扫描", "MAIN")
            sys.exit(1)
        except Exception as e:
            log.error(f"程序执行失败: {e}")
            if self.args.verbose:
                import traceback
                log.error(traceback.format_exc())
            sys.exit(1)

def main():
    """主入口函数"""
    app = OptimizedFnuclei()
    app.run()

if __name__ == '__main__':
    main()
