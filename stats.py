#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计和性能监控模块
"""

import time
import os
import sys
from datetime import datetime
from collections import defaultdict
sys.path.append(os.path.dirname(__file__))
from logger_config import log

class ScanStats:
    """扫描统计类"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
        self.fingerprint_stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'unknown': 0
        }
        self.nuclei_stats = {
            'total_targets': 0,
            'matched_targets': 0,
            'vulnerabilities_found': 0,
            'scanned_tags': set()
        }
        self.cms_distribution = defaultdict(int)
        self.performance = {
            'fingerprint_time': 0,
            'nuclei_time': 0,
            'total_time': 0
        }
    
    def start_scan(self):
        """开始扫描计时"""
        self.start_time = time.time()
        log.info("扫描统计开始", "STATS")
    
    def end_scan(self):
        """结束扫描计时"""
        self.end_time = time.time()
        self.performance['total_time'] = self.end_time - self.start_time
        self._print_summary()
    
    def add_fingerprint_result(self, result):
        """添加指纹识别结果"""
        self.fingerprint_stats['total'] += 1
        
        if result['cms'] == '':
            self.fingerprint_stats['failed'] += 1
        elif result['cms'] == 'unknown':
            self.fingerprint_stats['unknown'] += 1
        else:
            self.fingerprint_stats['success'] += 1
            self.cms_distribution[result['cms']] += 1
    
    def add_nuclei_result(self, tag, has_vulnerability=False):
        """添加Nuclei扫描结果"""
        self.nuclei_stats['scanned_tags'].add(tag)
        if has_vulnerability:
            self.nuclei_stats['vulnerabilities_found'] += 1
    
    def set_nuclei_targets(self, total, matched):
        """设置Nuclei目标统计"""
        self.nuclei_stats['total_targets'] = total
        self.nuclei_stats['matched_targets'] = matched
    
    def set_fingerprint_time(self, duration):
        """设置指纹识别耗时"""
        self.performance['fingerprint_time'] = duration
    
    def set_nuclei_time(self, duration):
        """设置Nuclei扫描耗时"""
        self.performance['nuclei_time'] = duration
    
    def _print_summary(self):
        """打印统计摘要"""
        log.info("=" * 60, "STATS")
        log.info("扫描统计摘要", "STATS")
        log.info("=" * 60, "STATS")
        
        # 时间统计
        log.info(f"总耗时: {self.performance['total_time']:.2f}秒", "STATS")
        log.info(f"指纹识别耗时: {self.performance['fingerprint_time']:.2f}秒", "STATS")
        log.info(f"Nuclei扫描耗时: {self.performance['nuclei_time']:.2f}秒", "STATS")
        
        # 指纹识别统计
        fp_stats = self.fingerprint_stats
        log.info(f"指纹识别: 总计{fp_stats['total']} | 成功{fp_stats['success']} | 失败{fp_stats['failed']} | 未识别{fp_stats['unknown']}", "STATS")
        
        # CMS分布
        if self.cms_distribution:
            log.info("CMS分布:", "STATS")
            for cms, count in sorted(self.cms_distribution.items(), key=lambda x: x[1], reverse=True)[:5]:
                log.info(f"  {cms}: {count}", "STATS")
        
        # Nuclei统计
        nuclei_stats = self.nuclei_stats
        log.info(f"Nuclei扫描: 匹配目标{nuclei_stats['matched_targets']}/{nuclei_stats['total_targets']} | 发现漏洞{nuclei_stats['vulnerabilities_found']} | 扫描标签{len(nuclei_stats['scanned_tags'])}", "STATS")
        
        log.info("=" * 60, "STATS")

# 全局统计实例
stats = ScanStats()
