import json
import requests
import hashlib
import base64
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from concurrent.futures import ThreadP<PERSON>Executor, as_completed
import argparse


# 加载指纹
def load_fingerprints(file='finger.json'):
    with open(file, 'r', encoding='utf-8') as f:
        data = json.load(f)
        if"fingerprint" in data:
            return data["fingerprint"]
        raise ValueError("指纹文件格式不正确，应包含 'fingerprint' 字段。")

# 获取 HTTP 响应
def get_http_response(url):
    try:
        headers = {
            "User-Agent": "Mozilla/5.0"
        }
        return requests.get(url, headers=headers, timeout=8, verify=False)
    except:
        return None

# 计算 favicon hash
def get_favicon_hash(url):
    try:
        favicon_url = urljoin(url, '/favicon.ico')
        res = requests.get(favicon_url, timeout=5, verify=False)
        if res.status_code == 200:
            m = hashlib.md5()
            b64 = base64.b64encode(res.content)
            m.update(b64)
            return int(m.hexdigest(), 16)
    except:
        return None

# 匹配单条指纹
def match_one(fpr, res, fav_hash):
    method = fpr["method"]
    loc = fpr.get("location", "body").lower()
    kws = fpr["keyword"]

    if method == 'keyword':
        text_body = res.text or""
        text_head = "\n".join(f"{k}: {v}"for k, v in res.headers.items())

        # 处理 title 和 header 等
        if loc == 'header':
            spaces = [text_head, text_body]
        elif loc == 'title':
            soup = BeautifulSoup(text_body, "html.parser")
            title = soup.title.string if soup.title and soup.title.string else ""
            spaces = [title, text_body]
        elif loc == 'body':
            spaces = [text_body]
        else:
            spaces = [text_body]

        for space in spaces:
            for kw in kws:
                if kw.lower() in space.lower():
                    return True

    if method == 'faviconhash'and fav_hash is not None:
        for kw in kws:
            try:
                if fav_hash == int(kw):
                    return True
            except:
                continue

    return False

# 识别单个 URL 指纹
def match_fingerprint(url, fps=None):
    fps = fps or load_fingerprints()
    res = get_http_response(url)
    fav_hash = get_favicon_hash(url)

    matched = []
    for fpr in fps:
        if 'cms' not in fpr or 'method' not in fpr or 'keyword' not in fpr:
            continue
        if match_one(fpr, res, fav_hash):
            matched.append(fpr['cms'])

    print(f"[✓] {url} 指纹识别结果：{list(set(matched))}")
    return {url: list(set(matched))}

# 多线程执行
def run_multithread(urls, threads):
    fps = load_fingerprints()
    results = []
    with ThreadPoolExecutor(max_workers=threads) as executor:
        future_to_url = {executor.submit(match_fingerprint, url, fps): url for url in urls}
        for future in as_completed(future_to_url):
            results.append(future.result())
    return results

# 主程序入口
def main():
    parser = argparse.ArgumentParser(description="指纹识别脚本 - 支持多线程")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("-u", "--url", help="单个目标 URL")
    group.add_argument("-f", "--file", help="包含多个 URL 的文件")
    parser.add_argument("-t", "--threads", type=int, default=10, help="线程数（默认10）")
    args = parser.parse_args()

    if args.url:
        match_fingerprint(args.url)

    elif args.file:
        with open(args.file, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]
        results = run_multithread(urls, args.threads)

if __name__ == '__main__':
    main()
