import json
import os
import threading
import time
import base64
import hashlib
import requests
import argparse
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue

requests.packages.urllib3.disable_warnings()

# ---------- 指纹识别部分 ----------

def load_fingerprints(file='finger.json'):
    with open(file, 'r', encoding='utf-8') as f:
        data = json.load(f)
        return data["fingerprint"]

def get_http_response(url):
    try:
        headers = {"User-Agent": "Mozilla/5.0"}
        return requests.get(url, headers=headers, timeout=8, verify=False)
    except:
        return None

def get_favicon_hash(url):
    try:
        favicon_url = urljoin(url, '/favicon.ico')
        res = requests.get(favicon_url, timeout=5, verify=False)
        if res.status_code == 200:
            m = hashlib.md5()
            b64 = base64.b64encode(res.content)
            m.update(b64)
            return int(m.hexdigest(), 16)
    except:
        return None

def match_one(fpr, res, fav_hash):
    method = fpr["method"]
    loc = fpr.get("location", "body").lower()
    kws = fpr["keyword"]

    if method == 'keyword':
        text_body = res.text or ""
        text_head = "\n".join(f"{k}: {v}"for k, v in res.headers.items())
        if loc == 'header':
            spaces = [text_head]
        elif loc == 'title':
            soup = BeautifulSoup(text_body, "html.parser")
            title = soup.title.string if soup.title and soup.title.string else ""
            spaces = [title]
        else:
            spaces = [text_body]

        for space in spaces:
            for kw in kws:
                if kw.lower() in space.lower():
                    return True

    elif method == 'faviconhash'and fav_hash is not None:
        for kw in kws:
            try:
                if fav_hash == int(kw):
                    return True
            except:
                continue

    return False

def detect_basic_tech(res):
    """基础技术栈检测，通过响应头和内容判断"""
    detected = []

    if res is None:
        return detected

    # 检查响应头
    headers = res.headers
    content = res.text.lower()

    # 服务器检测
    server = headers.get('Server', '').lower()
    if 'apache' in server:
        detected.append('apache')
    elif 'nginx' in server:
        detected.append('nginx')
    elif 'iis' in server:
        detected.append('iis')
    elif 'tomcat' in server:
        detected.append('tomcat')
    elif 'jetty' in server:
        detected.append('jetty')

    # 技术栈检测
    if 'jsessionid' in content or 'jsessionid' in str(headers):
        detected.append('java')
    if 'phpsessid' in content or 'php' in server:
        detected.append('php')
    if 'asp.net' in content or 'aspnet' in server:
        detected.append('aspnet')
    if 'x-powered-by' in headers:
        powered_by = headers['x-powered-by'].lower()
        if 'php' in powered_by:
            detected.append('php')
        elif 'asp.net' in powered_by:
            detected.append('aspnet')

    # CMS检测
    if 'wp-content' in content or 'wordpress' in content:
        detected.append('wordpress')
    if 'joomla' in content:
        detected.append('joomla')
    if 'drupal' in content:
        detected.append('drupal')

    # 框架检测
    if 'spring' in content:
        detected.append('spring')
    if 'struts' in content:
        detected.append('struts')
    if 'laravel' in content:
        detected.append('laravel')
    if 'django' in content:
        detected.append('django')

    return detected

def match_fingerprint(url, fps):
    res = get_http_response(url)
    if res is None:
        print(f"[!] 无法访问 {url}")
        return {"url": url, "cms": ""}

    fav_hash = get_favicon_hash(url)
    matched = []

    for fpr in fps:
        if'cms' not in fpr or'method' not in fpr or'keyword' not in fpr:
            continue
        if match_one(fpr, res, fav_hash):
            matched.append(fpr['cms'])

    # 如果没有匹配到指纹，尝试通过响应头和内容进行基础判断
    if not matched:
        matched = detect_basic_tech(res)

    print(f"[✓] {url} 指纹识别结果：{list(set(matched))}")
    return {"url": url, "cms": list(set(matched))[0] if matched else "unknown"}

def run_fingerprint_scan(urls, threads, output='res.json'):
    fps = load_fingerprints()
    results = []

    with ThreadPoolExecutor(max_workers=threads) as executor:
        future_to_url = {executor.submit(match_fingerprint, url, fps): url for url in urls}
        for future in as_completed(future_to_url):
            results.append(future.result())

    with open(output, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

# ---------- Nuclei 扫描部分 ----------

class AutoNuclei:
    def __init__(self, res_file='res.json', tag_file='/home/<USER>/TEMPLATES-STATS.json', thread_count=5):
        self.res_file = res_file
        self.tag_file = tag_file
        self.havetag_file = 'havetag.txt'
        self.notag_file = 'notag.txt'
        self.result_dir = 'result'
        self.thread_count = thread_count

        self.cms_targets = {}         # {cms: [url1, url2]}
        self.nuclei_tags = set()
        self.tagged_targets = {}      # {tag: [url1, url2]}
        self.untagged_targets = []

        self.task_queue = Queue()
        self.load_res_json()
        self.load_tags()
        self.classify_targets()
        self.save_targets()
        self.start_scan_threads()

    def load_res_json(self):
        with open(self.res_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        for entry in data:
            cms = entry.get("cms", "").lower()
            url = entry.get("url")
            if cms and url:
                self.cms_targets.setdefault(cms, []).append(url)

    def load_tags(self):
        try:
            with open(self.tag_file, 'r', encoding='utf-8') as f:
                tags_data = json.load(f)
            for item in tags_data.get("tags", []):
                if item["name"]:
                    self.nuclei_tags.add(item["name"].lower())
        except FileNotFoundError:
            print(f"[!] 警告: 标签文件 {self.tag_file} 不存在，将跳过标签匹配")
        except json.JSONDecodeError:
            print(f"[!] 警告: 标签文件 {self.tag_file} 格式错误，将跳过标签匹配")

    def classify_targets(self):
        for cms, urls in self.cms_targets.items():
            if cms in self.nuclei_tags:
                self.tagged_targets.setdefault(cms, []).extend(urls)
            elif cms == "unknown":
                # 对未识别的目标进行通用扫描
                self.tagged_targets.setdefault("generic", []).extend(urls)
            else:
                self.untagged_targets.extend(urls)

        # 如果没有任何标签匹配，对所有目标进行基础扫描
        if not self.tagged_targets and self.untagged_targets:
            print("[*] 未找到匹配的标签，将进行通用漏洞扫描...")
            self.tagged_targets["generic"] = self.untagged_targets.copy()
            self.untagged_targets = []

    def save_targets(self):
        with open(self.havetag_file, 'w', encoding='utf-8') as f:
            for tag, urls in self.tagged_targets.items():
                for url in urls:
                    f.write(f"{tag}||{url}\n")

        with open(self.notag_file, 'w', encoding='utf-8') as f:
            for url in self.untagged_targets:
                f.write(url + '\n')

        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir)

    def scan_worker(self):
        while not self.task_queue.empty():
            try:
                tag, url = self.task_queue.get(timeout=1)
                target_file = f"temp_{int(time.time() * 1000)}.txt"
                with open(target_file, 'w', encoding='utf-8') as f:
                    f.write(url)

                output_file = os.path.join(self.result_dir, f"{tag}_{int(time.time())}.txt")

                # 根据标签类型选择不同的扫描参数
                if tag == "generic":
                    # 通用扫描：使用常见的漏洞类型
                    cmd = f"nuclei -l {target_file} -tags cve,exposure,misconfiguration -severity critical,high,medium -o {output_file} -stats"
                    print(f"[+] 通用漏洞扫描启动: {url}")
                elif tag in ["spring-boot", "spring-boot-actuator", "java-web"]:
                    # Spring Boot 相关扫描
                    cmd = f"nuclei -l {target_file} -tags exposure,misconfiguration,springboot -severity critical,high,medium -o {output_file} -stats"
                    print(f"[+] Spring Boot 漏洞扫描启动: {url}")
                else:
                    cmd = f"nuclei -l {target_file} -tags {tag} -o {output_file} -stats"
                    print(f"[+] 扫描任务启动: {url} -> {tag}")

                # 使用subprocess替代os.system以获得更好的错误处理
                import subprocess
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
                    if result.returncode != 0:
                        print(f"[!] Nuclei扫描失败: {result.stderr}")
                except subprocess.TimeoutExpired:
                    print(f"[!] 扫描超时: {url}")
                except Exception as e:
                    print(f"[!] 扫描错误: {e}")
                finally:
                    if os.path.exists(target_file):
                        os.remove(target_file)
            except Exception as e:
                print(f"[!] 线程错误: {e}")

    def start_scan_threads(self):
        for tag, urls in self.tagged_targets.items():
            for url in urls:
                self.task_queue.put((tag, url))
        threads = []
        for _ in range(self.thread_count):
            t = threading.Thread(target=self.scan_worker)
            t.start()
            threads.append(t)
        for t in threads:
            t.join()
        print("[✓] 所有扫描任务完成！")

# ---------- 辅助函数 ----------

def create_simple_tags_file():
    """创建一个简化的标签映射文件，包含常见的CMS和技术栈标签"""
    simple_tags = {
        "tags": [
            {"name": "spring-boot-actuator"},
            {"name": "spring-boot"},
            {"name": "spring"},
            {"name": "java-web"},
            {"name": "actuator"},
            {"name": "exposure"},
            {"name": "misconfiguration"},
            {"name": "wordpress"},
            {"name": "joomla"},
            {"name": "drupal"},
            {"name": "apache"},
            {"name": "nginx"},
            {"name": "iis"},
            {"name": "tomcat"},
            {"name": "jenkins"},
            {"name": "phpmyadmin"},
            {"name": "mysql"},
            {"name": "postgresql"},
            {"name": "mongodb"},
            {"name": "redis"},
            {"name": "elasticsearch"},
            {"name": "kibana"},
            {"name": "grafana"},
            {"name": "prometheus"},
            {"name": "docker"},
            {"name": "kubernetes"},
            {"name": "gitlab"},
            {"name": "github"},
            {"name": "bitbucket"},
            {"name": "confluence"},
            {"name": "jira"},
            {"name": "sharepoint"},
            {"name": "exchange"},
            {"name": "outlook"},
            {"name": "magento"},
            {"name": "prestashop"},
            {"name": "opencart"},
            {"name": "woocommerce"},
            {"name": "shopify"},
            {"name": "laravel"},
            {"name": "symfony"},
            {"name": "codeigniter"},
            {"name": "cakephp"},
            {"name": "django"},
            {"name": "flask"},
            {"name": "rails"},
            {"name": "spring"},
            {"name": "struts"},
            {"name": "nodejs"},
            {"name": "react"},
            {"name": "angular"},
            {"name": "vue"},
            {"name": "jquery"},
            {"name": "bootstrap"},
            {"name": "php"},
            {"name": "java"},
            {"name": "python"},
            {"name": "ruby"},
            {"name": "perl"},
            {"name": "asp"},
            {"name": "aspnet"},
            {"name": "coldfusion"},
            {"name": "weblogic"},
            {"name": "websphere"},
            {"name": "glassfish"},
            {"name": "jetty"},
            {"name": "lighttpd"},
            {"name": "caddy"},
            {"name": "haproxy"},
            {"name": "varnish"},
            {"name": "cloudflare"},
            {"name": "aws"},
            {"name": "azure"},
            {"name": "gcp"},
            {"name": "oracle"},
            {"name": "sap"},
            {"name": "citrix"},
            {"name": "vmware"},
            {"name": "fortinet"},
            {"name": "pfsense"},
            {"name": "mikrotik"},
            {"name": "cisco"},
            {"name": "juniper"},
            {"name": "f5"},
            {"name": "netgear"},
            {"name": "dlink"},
            {"name": "tplink"},
            {"name": "asus"},
            {"name": "linksys"},
            {"name": "ubiquiti"},
            {"name": "sophos"},
            {"name": "paloalto"},
            {"name": "checkpoint"},
            {"name": "sonicwall"},
            {"name": "watchguard"},
            {"name": "barracuda"},
            {"name": "mcafee"},
            {"name": "symantec"},
            {"name": "kaspersky"},
            {"name": "trend"},
            {"name": "eset"},
            {"name": "avast"},
            {"name": "avg"},
            {"name": "bitdefender"},
            {"name": "malwarebytes"},
            {"name": "webmin"},
            {"name": "cpanel"},
            {"name": "plesk"},
            {"name": "directadmin"},
            {"name": "ispconfig"},
            {"name": "virtualmin"},
            {"name": "zimbra"},
            {"name": "roundcube"},
            {"name": "squirrelmail"},
            {"name": "horde"},
            {"name": "owncloud"},
            {"name": "nextcloud"},
            {"name": "seafile"},
            {"name": "pydio"},
            {"name": "ajenti"},
            {"name": "cockpit"},
            {"name": "portainer"},
            {"name": "rancher"},
            {"name": "openshift"},
            {"name": "mesos"},
            {"name": "consul"},
            {"name": "vault"},
            {"name": "terraform"},
            {"name": "ansible"},
            {"name": "puppet"},
            {"name": "chef"},
            {"name": "saltstack"},
            {"name": "nagios"},
            {"name": "zabbix"},
            {"name": "cacti"},
            {"name": "prtg"},
            {"name": "solarwinds"},
            {"name": "splunk"},
            {"name": "logstash"},
            {"name": "fluentd"},
            {"name": "graylog"},
            {"name": "rsyslog"},
            {"name": "syslog"}
        ]
    }

    with open('./simple-tags.json', 'w', encoding='utf-8') as f:
        json.dump(simple_tags, f, ensure_ascii=False, indent=2)

    print("[✓] 已创建简化标签文件: simple-tags.json")

# ---------- 主程序入口 ----------

def main():
    parser = argparse.ArgumentParser(description="指纹识别 + Nuclei自动化工具")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("-u", "--url", help="目标 URL")
    group.add_argument("-f", "--file", help="URL列表文件")
    parser.add_argument("--fp-threads", type=int, default=10, help="指纹识别线程数")
    parser.add_argument("--scan-threads", type=int, default=5, help="Nuclei 扫描线程数")
    args = parser.parse_args()

    urls = []
    if args.url:
        urls = [args.url]
    elif args.file:
        with open(args.file, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]

    print("[*] 正在执行指纹识别...")
    run_fingerprint_scan(urls, threads=args.fp_threads, output='res.json')

    print("[*] 指纹识别完成，开始 Nuclei 扫描...")
    # 尝试多个可能的nuclei-templates路径
    possible_paths = [
        '/home/<USER>/nuclei-templates/TEMPLATES-STATS.json',
        os.path.expanduser('~/nuclei-templates/TEMPLATES-STATS.json'),
        '/usr/share/nuclei-templates/TEMPLATES-STATS.json',
        './nuclei-templates/TEMPLATES-STATS.json'
    ]

    tag_file = None
    for path in possible_paths:
        if os.path.exists(path):
            tag_file = path
            break

    if not tag_file:
        print("[!] 警告: 未找到 TEMPLATES-STATS.json 文件")
        print("[*] 正在尝试生成简化的标签映射...")
        # 创建一个简化的标签文件
        create_simple_tags_file()
        tag_file = './simple-tags.json'

    AutoNuclei(
        res_file='res.json',
        tag_file=tag_file,
        thread_count=args.scan_threads
    )

if __name__ == '__main__':
    main()