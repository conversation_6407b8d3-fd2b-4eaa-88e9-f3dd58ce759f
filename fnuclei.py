#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指纹识别 + Nuclei自动化扫描工具
只扫描匹配到标签的站点
"""

import json
import os
import threading
import time
import base64
import hashlib
import requests
import argparse
import subprocess
from urllib.parse import urljoin
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue

# 导入日志模块
from script.logger_config import log

# 禁用SSL警告
requests.packages.urllib3.disable_warnings()

# ---------- 指纹识别类 ----------

class FingerprintScanner:
    """指纹识别扫描器类"""

    def __init__(self, fingerprint_file='data/finger.json'):
        self.fingerprint_file = fingerprint_file
        self.fingerprints = self._load_fingerprints()

    def _load_fingerprints(self):
        """加载指纹数据"""
        if not os.path.exists(self.fingerprint_file):
            raise FileNotFoundError(f"未找到指纹文件: {self.fingerprint_file}")

        with open(self.fingerprint_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data["fingerprint"]

    def _get_http_response(self, url):
        """获取HTTP响应"""
        try:
            headers = {"User-Agent": "Mozilla/5.0"}
            return requests.get(url, headers=headers, timeout=8, verify=False)
        except:
            return None

    def _get_favicon_hash(self, url):
        """获取favicon哈希值"""
        try:
            favicon_url = urljoin(url, '/favicon.ico')
            res = requests.get(favicon_url, timeout=5, verify=False)
            if res.status_code == 200:
                m = hashlib.md5()
                b64 = base64.b64encode(res.content)
                m.update(b64)
                return int(m.hexdigest(), 16)
        except:
            return None

    def _match_one(self, fpr, res, fav_hash):
        """匹配单个指纹"""
        method = fpr["method"]
        kws = fpr["keyword"]

        if method == 'keyword':
            # 简化匹配逻辑，主要检查响应体
            content = res.text or ""
            for kw in kws:
                if kw.lower() in content.lower():
                    return True

        elif method == 'faviconhash' and fav_hash is not None:
            for kw in kws:
                try:
                    if fav_hash == int(kw):
                        return True
                except:
                    continue

        return False

    def _detect_basic_tech(self, res):
        """简化的基础技术栈检测"""
        if res is None:
            return []

        detected = []
        content = res.text.lower()

        # 只检测常见的CMS和框架
        if 'wordpress' in content or 'wp-content' in content:
            detected.append('wordpress')
        if 'joomla' in content:
            detected.append('joomla')
        if 'drupal' in content:
            detected.append('drupal')

        return detected

    def scan_url(self, url):
        """扫描单个URL的指纹"""
        res = self._get_http_response(url)
        if res is None:
            log.error(f"无法访问 {url}")
            return {"url": url, "cms": ""}

        fav_hash = self._get_favicon_hash(url)
        matched = []

        # 匹配指纹库中的指纹
        for fpr in self.fingerprints:
            if 'cms' not in fpr or 'method' not in fpr or 'keyword' not in fpr:
                continue
            if self._match_one(fpr, res, fav_hash):
                matched.append(fpr['cms'])

        # 如果没有匹配到指纹，尝试基础检测
        if not matched:
            matched = self._detect_basic_tech(res)

        # 输出识别结果
        log.fingerprint_result(url, list(set(matched)))
        return {"url": url, "cms": list(set(matched))[0] if matched else "unknown"}

    def scan_urls(self, urls, threads=10, output='temp/res.json'):
        """批量扫描URL列表"""
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output), exist_ok=True)

        results = []
        with ThreadPoolExecutor(max_workers=threads) as executor:
            future_to_url = {executor.submit(self.scan_url, url): url for url in urls}
            for future in as_completed(future_to_url):
                results.append(future.result())

        # 保存结果
        with open(output, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        return output

# ---------- Nuclei 扫描部分 ----------

class AutoNuclei:
    def __init__(self, res_file=None, tag_file=None, thread_count=5):
        # 设置默认的结果文件路径
        if res_file is None:
            res_file = 'temp/res.json'

        self.res_file = res_file
        self.tag_file = tag_file or self._find_tags_file()
        self.thread_count = thread_count
        self.result_dir = 'output'  # 结果保存到output目录

        self.cms_targets = {}         # {cms: [url1, url2]}
        self.nuclei_tags = set()
        self.tagged_targets = {}      # {tag: [url1, url2]}

        self.task_queue = Queue()
        self.load_res_json()
        self.load_tags()
        self.classify_targets()
        self.start_scan_threads()

    def _find_tags_file(self):
        """查找标签文件"""
        tag_file = 'data/nuclei-tags.json'
        if os.path.exists(tag_file):
            return tag_file

        log.error("未找到标签文件，无法进行扫描")
        return None

    def load_res_json(self):
        with open(self.res_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        for entry in data:
            cms = entry.get("cms", "").lower()
            url = entry.get("url")
            if cms and url:
                self.cms_targets.setdefault(cms, []).append(url)

    def load_tags(self):
        if not self.tag_file:
            return

        try:
            with open(self.tag_file, 'r', encoding='utf-8') as f:
                tags_data = json.load(f)
            for item in tags_data.get("tags", []):
                if item["name"]:
                    self.nuclei_tags.add(item["name"].lower())
        except FileNotFoundError:
            log.warning(f"标签文件 {self.tag_file} 不存在，将跳过标签匹配")
        except json.JSONDecodeError:
            log.warning(f"标签文件 {self.tag_file} 格式错误，将跳过标签匹配")

    def classify_targets(self):
        """只分类匹配到标签的目标，不进行通用扫描"""
        for cms, urls in self.cms_targets.items():
            if cms in self.nuclei_tags:
                self.tagged_targets.setdefault(cms, []).extend(urls)

        # 统计结果
        total_targets = sum(len(urls) for urls in self.cms_targets.values())
        matched_targets = sum(len(urls) for urls in self.tagged_targets.values())

        if matched_targets > 0:
            log.success(f"找到 {matched_targets}/{total_targets} 个匹配标签的目标", "MATCH")
        else:
            log.warning(f"未找到任何匹配标签的目标 (0/{total_targets})", "MATCH")

    def save_targets(self):
        with open(self.havetag_file, 'w', encoding='utf-8') as f:
            for tag, urls in self.tagged_targets.items():
                for url in urls:
                    f.write(f"{tag}||{url}\n")

        with open(self.notag_file, 'w', encoding='utf-8') as f:
            for url in self.untagged_targets:
                f.write(url + '\n')

        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir)

    def scan_worker(self):
        while not self.task_queue.empty():
            try:
                tag, url = self.task_queue.get(timeout=1)
                target_file = f"temp/target_{int(time.time() * 1000)}.txt"
                with open(target_file, 'w', encoding='utf-8') as f:
                    f.write(url)

                # 使用 tags_当前日期 格式保存结果
                from datetime import datetime
                current_date = datetime.now().strftime('%Y%m%d')
                output_file = os.path.join(self.result_dir, f"{tag}_{current_date}.txt")
                cmd = f"nuclei -l {target_file} -tags {tag} -o {output_file} -stats"

                log.scan_start(url, tag)

                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
                    if result.returncode != 0:
                        log.error(f"Nuclei扫描失败: {result.stderr}")
                    else:
                        # 检查输出文件是否有内容
                        has_vulnerabilities = os.path.exists(output_file) and os.path.getsize(output_file) > 0
                        log.nuclei_result(output_file, has_vulnerabilities)
                except subprocess.TimeoutExpired:
                    log.error(f"扫描超时: {url}")
                except Exception as e:
                    log.error(f"扫描错误: {e}")
                finally:
                    if os.path.exists(target_file):
                        os.remove(target_file)
            except Exception as e:
                log.error(f"线程错误: {e}")

    def start_scan_threads(self):
        if not self.tagged_targets:
            log.warning("没有匹配标签的目标，跳过扫描", "SKIP")
            return

        # 确保输出目录存在
        os.makedirs(self.result_dir, exist_ok=True)
        os.makedirs('temp', exist_ok=True)

        for tag, urls in self.tagged_targets.items():
            for url in urls:
                self.task_queue.put((tag, url))

        threads = []
        for _ in range(self.thread_count):
            t = threading.Thread(target=self.scan_worker)
            t.start()
            threads.append(t)
        for t in threads:
            t.join()
        log.success("所有扫描任务完成！", "COMPLETE")

# ---------- 主程序入口 ----------

def main():
    parser = argparse.ArgumentParser(description="指纹识别 + Nuclei自动化工具")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("-u", "--url", help="目标 URL")
    group.add_argument("-f", "--file", help="URL列表文件")
    parser.add_argument("--fp-threads", type=int, default=10, help="指纹识别线程数")
    parser.add_argument("--scan-threads", type=int, default=5, help="Nuclei 扫描线程数")
    args = parser.parse_args()

    # 获取URL列表
    urls = [args.url] if args.url else [line.strip() for line in open(args.file, 'r', encoding='utf-8') if line.strip()]

    # 执行指纹识别
    log.info("正在执行指纹识别...", "FINGERPRINT")
    scanner = FingerprintScanner()
    res_file = scanner.scan_urls(urls, threads=args.fp_threads)

    # 执行Nuclei扫描
    log.info("指纹识别完成，开始 Nuclei 扫描...", "NUCLEI")
    AutoNuclei(res_file=res_file, thread_count=args.scan_threads)

if __name__ == '__main__':
    main()