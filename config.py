#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
统一管理所有配置项
"""

import os
from datetime import datetime

class Config:
    """配置类"""
    
    # 文件路径配置
    FINGERPRINT_FILE = 'data/finger.json'
    NUCLEI_TAGS_FILE = 'data/nuclei-tags.json'
    
    # 目录配置
    DATA_DIR = 'data'
    OUTPUT_DIR = 'output'
    TEMP_DIR = 'temp'
    LOGS_DIR = 'logs'
    
    # 扫描配置
    DEFAULT_FP_THREADS = 10
    DEFAULT_SCAN_THREADS = 5
    HTTP_TIMEOUT = 8
    FAVICON_TIMEOUT = 5
    NUCLEI_TIMEOUT = 300
    
    # 日志配置
    LOG_ROTATION = "10 MB"
    LOG_RETENTION = "7 days"
    LOG_FORMAT_CONSOLE = "<green>{time:HH:mm:ss}</green> | <cyan>{extra[script]}</cyan>:<yellow>{extra[module]}</yellow> | <level>{level: <8}</level> | <level>{message}</level>"
    LOG_FORMAT_FILE = "{time:YYYY-MM-DD HH:mm:ss} | {extra[script]}:{extra[module]} | {level: <8} | {message}"
    
    # HTTP请求配置
    USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    
    @staticmethod
    def get_result_filename(tag):
        """生成结果文件名"""
        current_date = datetime.now().strftime('%Y%m%d')
        return f"{tag}_{current_date}.txt"
    
    @staticmethod
    def get_log_filename(app_name):
        """生成日志文件名"""
        current_date = datetime.now().strftime('%Y%m%d')
        return f"{app_name}_{current_date}.log"
    
    @staticmethod
    def ensure_dirs():
        """确保所有必要目录存在"""
        dirs = [Config.DATA_DIR, Config.OUTPUT_DIR, Config.TEMP_DIR, Config.LOGS_DIR]
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
