#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目功能测试脚本
"""

import os
import json
from loguru import logger

# 配置 loguru 日志
logger.remove()
logger.add(
    "logs/test.log",
    rotation="10 MB",
    retention="7 days",
    encoding="utf-8",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
)
logger.add(
    lambda msg: print(msg, end=""),
    format="{time:HH:mm:ss} | <level>{level}</level> | {message}",
    colorize=True
)

def test_directory_structure():
    """测试目录结构"""
    logger.info("测试目录结构...")
    
    required_dirs = [
        'src',
        'config', 
        'data/fingerprints',
        'data/tags',
        'results',
        'logs',
        'docs'
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            logger.success(f"✓ 目录存在: {dir_path}")
        else:
            logger.error(f"✗ 目录缺失: {dir_path}")

def test_required_files():
    """测试必需文件"""
    logger.info("测试必需文件...")
    
    required_files = [
        'src/fnuclei.py',
        'src/actuator_scan.py', 
        'data/fingerprints/finger.json',
        'data/tags/nuclei-tags.json',
        'config/config.ini',
        'requirements.txt',
        'README.md'
    ]
    
    for file_path in required_files:
        if os.path.exists(file_path):
            logger.success(f"✓ 文件存在: {file_path}")
        else:
            logger.error(f"✗ 文件缺失: {file_path}")

def test_json_files():
    """测试JSON文件格式"""
    logger.info("测试JSON文件格式...")
    
    json_files = [
        'data/fingerprints/finger.json',
        'data/tags/nuclei-tags.json'
    ]
    
    for file_path in json_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.success(f"✓ JSON格式正确: {file_path}")
                
                # 检查标签文件内容
                if 'nuclei-tags.json' in file_path:
                    tags_count = len(data.get('tags', []))
                    logger.info(f"  标签数量: {tags_count}")
                
                # 检查指纹文件内容
                if 'finger.json' in file_path:
                    fingerprints_count = len(data.get('fingerprint', []))
                    logger.info(f"  指纹数量: {fingerprints_count}")
                    
            except json.JSONDecodeError as e:
                logger.error(f"✗ JSON格式错误: {file_path} - {e}")
        else:
            logger.warning(f"⚠ 文件不存在: {file_path}")

def test_unwanted_files():
    """检查不应该存在的文件"""
    logger.info("检查不应该存在的文件...")
    
    unwanted_files = [
        'simple-tags.json',
        'res.json',  # 应该在 results/ 目录中
        'havetag.txt',
        'notag.txt',
        'fnuclei.log'  # 应该在 logs/ 目录中
    ]
    
    for file_path in unwanted_files:
        if os.path.exists(file_path):
            logger.warning(f"⚠ 不应该存在的文件: {file_path}")
        else:
            logger.success(f"✓ 文件已正确清理: {file_path}")

def test_import_modules():
    """测试模块导入"""
    logger.info("测试模块导入...")
    
    try:
        import sys
        sys.path.append('src')
        
        # 测试主模块导入
        logger.info("测试 fnuclei 模块导入...")
        import fnuclei
        logger.success("✓ fnuclei 模块导入成功")
        
        # 测试 actuator_scan 模块导入
        logger.info("测试 actuator_scan 模块导入...")
        import actuator_scan
        logger.success("✓ actuator_scan 模块导入成功")
        
    except ImportError as e:
        logger.error(f"✗ 模块导入失败: {e}")
    except Exception as e:
        logger.error(f"✗ 其他错误: {e}")

def main():
    """主测试函数"""
    logger.info("开始项目功能测试...")
    logger.info("=" * 50)
    
    test_directory_structure()
    logger.info("-" * 30)
    
    test_required_files()
    logger.info("-" * 30)
    
    test_json_files()
    logger.info("-" * 30)
    
    test_unwanted_files()
    logger.info("-" * 30)
    
    test_import_modules()
    logger.info("-" * 30)
    
    logger.info("项目功能测试完成！")

if __name__ == "__main__":
    main()
