# Auto Nuclei 配置文件

[fingerprint]
# 指纹识别配置
fingerprint_file = data/fingerprints/finger.json
threads = 10
timeout = 10

[nuclei]
# Nuclei 扫描配置
tags_file = data/tags/nuclei-tags.json
threads = 5
timeout = 300
severity = critical,high,medium

[output]
# 输出配置
results_dir = results
logs_dir = logs
format = json

[network]
# 网络配置
user_agent = Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
verify_ssl = false
