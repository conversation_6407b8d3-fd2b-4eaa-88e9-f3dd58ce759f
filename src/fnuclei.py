import json
import os
import sys
import threading
import time
import base64
import hashlib
import requests
import argparse
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue

# 添加scripts目录到路径，以便导入log_utils
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'scripts'))
from log_utils import LogHandler

# 初始化日志处理器
log_handler = LogHandler("fnuclei")
logger = log_handler.get_logger()

# 定义彩色日志函数
def log_info(message, tag=None):
    """信息日志 - 蓝色"""
    if tag:
        logger.info(f"<blue>[{tag}]</blue> {message}")
    else:
        logger.info(f"<blue>{message}</blue>")

def log_success(message, tag=None):
    """成功日志 - 绿色"""
    if tag:
        logger.success(f"<green>[{tag}]</green> <green>{message}</green>")
    else:
        logger.success(f"<green>✓ {message}</green>")

def log_warning(message, tag=None):
    """警告日志 - 黄色"""
    if tag:
        logger.warning(f"<yellow>[{tag}]</yellow> <yellow>{message}</yellow>")
    else:
        logger.warning(f"<yellow>⚠ {message}</yellow>")

def log_error(message, tag=None):
    """错误日志 - 红色"""
    if tag:
        logger.error(f"<red>[{tag}]</red> <red>{message}</red>")
    else:
        logger.error(f"<red>✗ {message}</red>")

def log_scan_start(url, scan_type):
    """扫描开始日志 - 青色"""
    logger.info(f"<cyan>🚀 扫描启动</cyan>: <white>{url}</white> -> <magenta>{scan_type}</magenta>")

def log_fingerprint_result(url, cms_list):
    """指纹识别结果日志"""
    if cms_list:
        cms_str = ", ".join(cms_list)
        logger.success(f"<green>🔍 指纹识别</green>: <white>{url}</white> -> <yellow>{cms_str}</yellow>")
    else:
        logger.warning(f"<yellow>🔍 指纹识别</yellow>: <white>{url}</white> -> <red>未识别</red>")

def log_nuclei_result(result_file, vulnerabilities_count=None):
    """Nuclei扫描结果日志"""
    if vulnerabilities_count and vulnerabilities_count > 0:
        logger.success(f"<green>🎯 发现漏洞</green>: <white>{result_file}</white> (<red>{vulnerabilities_count} 个漏洞</red>)")
    else:
        logger.info(f"<blue>📄 扫描完成</blue>: <white>{result_file}</white>")

requests.packages.urllib3.disable_warnings()

# ---------- 指纹识别部分 ----------

def load_fingerprints(file=None):
    if file is None:
        # 尝试多个可能的路径
        possible_paths = [
            '../data/fingerprints/finger.json',  # 从src目录运行
            'data/fingerprints/finger.json',     # 从项目根目录运行
            'finger.json'                        # 向后兼容
        ]

        for path in possible_paths:
            if os.path.exists(path):
                file = path
                break

        if file is None:
            raise FileNotFoundError("未找到指纹文件 finger.json")

    with open(file, 'r', encoding='utf-8') as f:
        data = json.load(f)
        return data["fingerprint"]

def get_http_response(url):
    try:
        headers = {"User-Agent": "Mozilla/5.0"}
        return requests.get(url, headers=headers, timeout=8, verify=False)
    except:
        return None

def get_favicon_hash(url):
    try:
        favicon_url = urljoin(url, '/favicon.ico')
        res = requests.get(favicon_url, timeout=5, verify=False)
        if res.status_code == 200:
            m = hashlib.md5()
            b64 = base64.b64encode(res.content)
            m.update(b64)
            return int(m.hexdigest(), 16)
    except:
        return None

def match_one(fpr, res, fav_hash):
    method = fpr["method"]
    loc = fpr.get("location", "body").lower()
    kws = fpr["keyword"]

    if method == 'keyword':
        text_body = res.text or ""
        text_head = "\n".join(f"{k}: {v}"for k, v in res.headers.items())
        if loc == 'header':
            spaces = [text_head]
        elif loc == 'title':
            soup = BeautifulSoup(text_body, "html.parser")
            title = soup.title.string if soup.title and soup.title.string else ""
            spaces = [title]
        else:
            spaces = [text_body]

        for space in spaces:
            for kw in kws:
                if kw.lower() in space.lower():
                    return True

    elif method == 'faviconhash'and fav_hash is not None:
        for kw in kws:
            try:
                if fav_hash == int(kw):
                    return True
            except:
                continue

    return False

def detect_basic_tech(res):
    """基础技术栈检测，通过响应头和内容判断"""
    detected = []

    if res is None:
        return detected

    # 检查响应头
    headers = res.headers
    content = res.text.lower()

    # 服务器检测
    server = headers.get('Server', '').lower()
    if 'apache' in server:
        detected.append('apache')
    elif 'nginx' in server:
        detected.append('nginx')
    elif 'iis' in server:
        detected.append('iis')
    elif 'tomcat' in server:
        detected.append('tomcat')
    elif 'jetty' in server:
        detected.append('jetty')

    # 技术栈检测
    if 'jsessionid' in content or 'jsessionid' in str(headers):
        detected.append('java')
    if 'phpsessid' in content or 'php' in server:
        detected.append('php')
    if 'asp.net' in content or 'aspnet' in server:
        detected.append('aspnet')
    if 'x-powered-by' in headers:
        powered_by = headers['x-powered-by'].lower()
        if 'php' in powered_by:
            detected.append('php')
        elif 'asp.net' in powered_by:
            detected.append('aspnet')

    # CMS检测
    if 'wp-content' in content or 'wordpress' in content:
        detected.append('wordpress')
    if 'joomla' in content:
        detected.append('joomla')
    if 'drupal' in content:
        detected.append('drupal')

    # 框架检测
    if 'spring' in content:
        detected.append('spring')
    if 'struts' in content:
        detected.append('struts')
    if 'laravel' in content:
        detected.append('laravel')
    if 'django' in content:
        detected.append('django')

    return detected

def match_fingerprint(url, fps):
    res = get_http_response(url)
    if res is None:
        log_error(f"无法访问 {url}")
        return {"url": url, "cms": ""}

    fav_hash = get_favicon_hash(url)
    matched = []

    for fpr in fps:
        if'cms' not in fpr or'method' not in fpr or'keyword' not in fpr:
            continue
        if match_one(fpr, res, fav_hash):
            matched.append(fpr['cms'])

    # 如果没有匹配到指纹，尝试通过响应头和内容进行基础判断
    if not matched:
        matched = detect_basic_tech(res)

    # 使用彩色日志输出指纹识别结果
    log_fingerprint_result(url, list(set(matched)))
    return {"url": url, "cms": list(set(matched))[0] if matched else "unknown"}

def run_fingerprint_scan(urls, threads, output=None):
    if output is None:
        output = '../results/res.json' if os.path.exists('../results') else 'results/res.json'

    # 确保输出目录存在
    os.makedirs(os.path.dirname(output), exist_ok=True)

    fps = load_fingerprints()
    results = []

    with ThreadPoolExecutor(max_workers=threads) as executor:
        future_to_url = {executor.submit(match_fingerprint, url, fps): url for url in urls}
        for future in as_completed(future_to_url):
            results.append(future.result())

    with open(output, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    return output

# ---------- Nuclei 扫描部分 ----------

class AutoNuclei:
    def __init__(self, res_file=None, tag_file=None, thread_count=5):
        # 设置默认的结果文件路径
        if res_file is None:
            res_file = '../results/res.json' if os.path.exists('../results/res.json') else 'results/res.json'

        self.res_file = res_file
        self.tag_file = tag_file or self._find_tags_file()
        self.thread_count = thread_count
        self.result_dir = '../results' if os.path.exists('../results') else 'results'

        self.cms_targets = {}         # {cms: [url1, url2]}
        self.nuclei_tags = set()
        self.tagged_targets = {}      # {tag: [url1, url2]}
        self.untagged_targets = []

        self.task_queue = Queue()
        self.load_res_json()
        self.load_tags()
        self.classify_targets()
        self.start_scan_threads()

    def _find_tags_file(self):
        """查找标签文件"""
        possible_paths = [
            '../data/tags/nuclei-tags.json',     # 从src目录运行
            'data/tags/nuclei-tags.json',        # 从项目根目录运行
            '/home/<USER>/nuclei-templates/TEMPLATES-STATS.json',
            os.path.expanduser('~/nuclei-templates/TEMPLATES-STATS.json'),
            '/usr/share/nuclei-templates/TEMPLATES-STATS.json',
            './nuclei-templates/TEMPLATES-STATS.json',
            'TEMPLATES-STATS.json'               # 向后兼容
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        # 如果都找不到，创建简化标签文件
        log_warning("未找到标签文件，将创建简化标签文件")
        self._create_simple_tags_file()
        return '../data/tags/nuclei-tags.json' if os.path.exists('../data/tags') else 'nuclei-tags.json'

    def _create_simple_tags_file(self):
        """创建简化的标签文件"""
        # 使用从指纹文件提取的标签
        tags_file = '../data/tags/nuclei-tags.json' if os.path.exists('../data/tags') else 'nuclei-tags.json'

        if os.path.exists(tags_file):
            return  # 文件已存在，不需要创建

        # 创建基本标签
        basic_tags = {
            "tags": [
                {"name": "spring-boot"},
                {"name": "spring"},
                {"name": "java"},
                {"name": "tomcat"},
                {"name": "weblogic"},
                {"name": "jboss"},
                {"name": "wordpress"},
                {"name": "joomla"},
                {"name": "drupal"},
                {"name": "apache"},
                {"name": "nginx"},
                {"name": "mysql"},
                {"name": "postgresql"},
                {"name": "redis"},
                {"name": "elasticsearch"},
                {"name": "jenkins"},
                {"name": "gitlab"},
                {"name": "docker"},
                {"name": "kubernetes"},
                {"name": "exposure"},
                {"name": "misconfiguration"},
                {"name": "cve"}
            ]
        }

        # 确保目录存在
        os.makedirs(os.path.dirname(tags_file) if os.path.dirname(tags_file) else '.', exist_ok=True)

        with open(tags_file, 'w', encoding='utf-8') as f:
            json.dump(basic_tags, f, ensure_ascii=False, indent=2)

        log_success(f"已创建简化标签文件: {tags_file}")

    def load_res_json(self):
        with open(self.res_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        for entry in data:
            cms = entry.get("cms", "").lower()
            url = entry.get("url")
            if cms and url:
                self.cms_targets.setdefault(cms, []).append(url)

    def load_tags(self):
        try:
            with open(self.tag_file, 'r', encoding='utf-8') as f:
                tags_data = json.load(f)
            for item in tags_data.get("tags", []):
                if item["name"]:
                    self.nuclei_tags.add(item["name"].lower())
        except FileNotFoundError:
            log_warning(f"标签文件 {self.tag_file} 不存在，将跳过标签匹配")
        except json.JSONDecodeError:
            log_warning(f"标签文件 {self.tag_file} 格式错误，将跳过标签匹配")

    def classify_targets(self):
        for cms, urls in self.cms_targets.items():
            if cms in self.nuclei_tags:
                self.tagged_targets.setdefault(cms, []).extend(urls)
            elif cms == "unknown":
                # 对未识别的目标进行通用扫描
                self.tagged_targets.setdefault("generic", []).extend(urls)
            else:
                self.untagged_targets.extend(urls)

        # 如果没有任何标签匹配，对所有目标进行基础扫描
        if not self.tagged_targets and self.untagged_targets:
            log_info("未找到匹配的标签，将进行通用漏洞扫描...", "SCAN")
            self.tagged_targets["generic"] = self.untagged_targets.copy()
            self.untagged_targets = []

    def save_targets(self):
        with open(self.havetag_file, 'w', encoding='utf-8') as f:
            for tag, urls in self.tagged_targets.items():
                for url in urls:
                    f.write(f"{tag}||{url}\n")

        with open(self.notag_file, 'w', encoding='utf-8') as f:
            for url in self.untagged_targets:
                f.write(url + '\n')

        if not os.path.exists(self.result_dir):
            os.makedirs(self.result_dir)

    def scan_worker(self):
        while not self.task_queue.empty():
            try:
                tag, url = self.task_queue.get(timeout=1)
                target_file = f"temp_{int(time.time() * 1000)}.txt"
                with open(target_file, 'w', encoding='utf-8') as f:
                    f.write(url)

                output_file = os.path.join(self.result_dir, f"{tag}_{int(time.time())}.txt")

                cmd = f"nuclei -l {target_file} -tags {tag} -o {output_file} -stats"
                log_scan_start(url, tag)
                logger.debug(f"扫描命令: {cmd}")


                # 使用subprocess替代os.system以获得更好的错误处理
                import subprocess
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
                    if result.returncode != 0:
                        log_error(f"Nuclei扫描失败: {result.stderr}")
                    else:
                        # 检查输出文件是否有内容
                        if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                            log_nuclei_result(output_file, "发现漏洞")
                        else:
                            log_nuclei_result(output_file)
                except subprocess.TimeoutExpired:
                    log_error(f"扫描超时: {url}")
                except Exception as e:
                    log_error(f"扫描错误: {e}")
                finally:
                    if os.path.exists(target_file):
                        os.remove(target_file)
            except Exception as e:
                log_error(f"线程错误: {e}")

    def start_scan_threads(self):
        for tag, urls in self.tagged_targets.items():
            for url in urls:
                self.task_queue.put((tag, url))
        threads = []
        for _ in range(self.thread_count):
            t = threading.Thread(target=self.scan_worker)
            t.start()
            threads.append(t)
        for t in threads:
            t.join()
        log_success("所有扫描任务完成！", "COMPLETE")

# ---------- 辅助函数 ----------

# ---------- 主程序入口 ----------

def main():
    parser = argparse.ArgumentParser(description="指纹识别 + Nuclei自动化工具")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("-u", "--url", help="目标 URL")
    group.add_argument("-f", "--file", help="URL列表文件")
    parser.add_argument("--fp-threads", type=int, default=10, help="指纹识别线程数")
    parser.add_argument("--scan-threads", type=int, default=5, help="Nuclei 扫描线程数")
    args = parser.parse_args()

    urls = []
    if args.url:
        urls = [args.url]
    elif args.file:
        with open(args.file, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip()]

    logger.info("正在执行指纹识别...")
    res_file = run_fingerprint_scan(urls, threads=args.fp_threads)

    logger.info("指纹识别完成，开始 Nuclei 扫描...")

    # 直接使用 AutoNuclei，它会自动查找标签文件
    AutoNuclei(
        res_file=res_file,
        thread_count=args.scan_threads
    )

if __name__ == '__main__':
    main()