# Auto Nuclei - 简化版本

## 🎯 主要改进

### 1. 简化扫描逻辑
- ✅ **只扫描匹配到标签的站点** - 不再进行通用漏洞扫描
- ✅ **移除不必要的功能** - 删除了基本标签创建、通用扫描等功能
- ✅ **精准匹配** - 只有指纹识别结果与nuclei标签匹配时才进行扫描

### 2. 独立日志模块
- ✅ **logger_config.py** - 独立的彩色日志配置模块
- ✅ **统一日志接口** - 所有日志调用通过 `log` 对象
- ✅ **彩色输出** - 使用emoji和颜色区分不同类型的日志

### 3. 代码结构优化
- ✅ **移除冗余代码** - 删除了不必要的函数和类方法
- ✅ **简化路径处理** - 统一使用相对路径
- ✅ **清晰的错误处理** - 改进了异常处理逻辑

## 📁 文件结构

```
auto_nuclei/
├── fnuclei.py              # 主程序 (简化版)
├── logger_config.py        # 日志配置模块
├── data/
│   ├── fingerprints/
│   │   └── finger.json     # 指纹数据
│   └── tags/
│       └── nuclei-tags.json # 标签数据
├── results/                # 扫描结果
└── logs/                   # 日志文件
```

## 🚀 使用方法

```bash
# 扫描单个URL
python3 fnuclei.py -u http://example.com

# 扫描URL列表
python3 fnuclei.py -f urls.txt

# 自定义线程数
python3 fnuclei.py -u http://example.com --fp-threads 20 --scan-threads 10
```

## 📊 日志输出示例

```
09:10:08 | INFO     | [FINGERPRINT] 正在执行指纹识别...
09:10:10 | SUCCESS  | 🔍 指纹识别: http://example.com -> wordpress, apache
09:10:10 | INFO     | [NUCLEI] 指纹识别完成，开始 Nuclei 扫描...
09:10:10 | SUCCESS  | [MATCH] 找到 1/1 个匹配标签的目标
09:10:11 | INFO     | 🚀 扫描启动: http://example.com -> wordpress
09:10:25 | SUCCESS  | 🎯 发现漏洞: results/wordpress_1749230892.txt
09:10:25 | SUCCESS  | [COMPLETE] 所有扫描任务完成！
```

## 🎨 日志类型

| 类型 | 图标 | 说明 |
|------|------|------|
| INFO | ℹ | 一般信息 |
| SUCCESS | ✓ | 成功操作 |
| WARNING | ⚠ | 警告信息 |
| ERROR | ✗ | 错误信息 |
| 指纹识别 | 🔍 | 指纹识别结果 |
| 扫描启动 | 🚀 | Nuclei扫描开始 |
| 发现漏洞 | 🎯 | 发现安全漏洞 |

## ⚡ 性能优化

1. **精准扫描** - 只扫描有匹配标签的目标，大幅减少扫描时间
2. **智能跳过** - 没有匹配目标时直接跳过，避免无效扫描
3. **清晰反馈** - 实时显示匹配统计和扫描进度

## 🔧 配置要求

- Python 3.6+
- loguru
- requests
- beautifulsoup4
- nuclei (已安装并在PATH中)

## 📝 更新日志

- v2.1: 简化扫描逻辑，只扫描匹配标签的目标
- v2.0: 独立日志模块，彩色输出
- v1.5: 添加Spring Boot Actuator专项扫描
- v1.0: 基础功能实现
