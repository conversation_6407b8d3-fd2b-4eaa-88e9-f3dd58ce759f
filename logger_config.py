#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置模块
提供统一的彩色日志输出功能
"""

import os
import sys
from loguru import logger

class ColorLogger:
    """彩色日志类"""
    
    def __init__(self, app_name="app"):
        self.app_name = app_name
        self._setup_logger()
    
    def _setup_logger(self):
        """配置日志设置"""
        # 移除默认处理器
        logger.remove()
        
        # 创建日志目录
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        # 日志文件路径
        log_file = os.path.join(log_dir, f"{self.app_name}.log")
        
        # 添加控制台输出 - 彩色格式
        logger.add(
            sys.stdout,
            format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
            level="INFO",
            colorize=True
        )
        
        # 添加文件输出 - 纯文本格式
        logger.add(
            log_file,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
            level="DEBUG",
            rotation="10 MB",
            retention="7 days",
            encoding="utf-8"
        )
    
    def info(self, message, tag=None):
        """信息日志"""
        if tag:
            logger.info(f"[{tag}] {message}")
        else:
            logger.info(f"ℹ {message}")
    
    def success(self, message, tag=None):
        """成功日志"""
        if tag:
            logger.success(f"[{tag}] {message}")
        else:
            logger.success(f"✓ {message}")
    
    def warning(self, message, tag=None):
        """警告日志"""
        if tag:
            logger.warning(f"[{tag}] {message}")
        else:
            logger.warning(f"⚠ {message}")
    
    def error(self, message, tag=None):
        """错误日志"""
        if tag:
            logger.error(f"[{tag}] {message}")
        else:
            logger.error(f"✗ {message}")
    
    def scan_start(self, url, scan_type):
        """扫描开始日志"""
        logger.info(f"🚀 扫描启动: {url} -> {scan_type}")
    
    def fingerprint_result(self, url, cms_list):
        """指纹识别结果日志"""
        if cms_list:
            cms_str = ", ".join(cms_list)
            logger.success(f"🔍 指纹识别: {url} -> {cms_str}")
        else:
            logger.warning(f"🔍 指纹识别: {url} -> 未识别")
    
    def nuclei_result(self, result_file, has_vulnerabilities=False):
        """Nuclei扫描结果日志"""
        if has_vulnerabilities:
            logger.success(f"🎯 发现漏洞: {result_file}")
        else:
            logger.info(f"📄 扫描完成: {result_file}")

# 创建全局日志实例
log = ColorLogger("fnuclei")
