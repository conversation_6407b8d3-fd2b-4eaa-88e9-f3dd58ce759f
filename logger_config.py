#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置模块
提供统一的彩色日志输出功能，包含脚本名和模块信息
"""

import os
import sys
import inspect
from datetime import datetime
from loguru import logger

class ColorLogger:
    """彩色日志类"""

    def __init__(self, app_name="app"):
        self.app_name = app_name
        self._setup_logger()

    def _setup_logger(self):
        """配置日志设置"""
        # 移除默认处理器
        logger.remove()

        # 创建日志目录
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)

        # 日志文件路径
        log_file = os.path.join(log_dir, f"{self.app_name}_{datetime.now().strftime('%Y%m%d')}.log")

        # 添加控制台输出 - 彩色格式，包含脚本和模块信息
        logger.add(
            sys.stdout,
            format="<green>{time:HH:mm:ss}</green> | <cyan>{extra[script]}</cyan>:<yellow>{extra[module]}</yellow> | <level>{level: <8}</level> | <level>{message}</level>",
            level="INFO",
            colorize=True
        )

        # 添加文件输出 - 纯文本格式
        logger.add(
            log_file,
            format="{time:YYYY-MM-DD HH:mm:ss} | {extra[script]}:{extra[module]} | {level: <8} | {message}",
            level="DEBUG",
            rotation="10 MB",
            retention="7 days",
            encoding="utf-8"
        )

    def _get_caller_info(self):
        """获取调用者信息"""
        frame = inspect.currentframe()
        try:
            # 向上查找调用栈，跳过当前方法和日志方法
            caller_frame = frame.f_back.f_back
            script_name = os.path.basename(caller_frame.f_code.co_filename).replace('.py', '')
            module_name = caller_frame.f_code.co_name
            return script_name, module_name
        finally:
            del frame
    
    def info(self, message, tag=None):
        """信息日志"""
        script_name, module_name = self._get_caller_info()
        if tag:
            logger.bind(script=script_name, module=module_name).info(f"[{tag}] {message}")
        else:
            logger.bind(script=script_name, module=module_name).info(f"ℹ {message}")

    def success(self, message, tag=None):
        """成功日志"""
        script_name, module_name = self._get_caller_info()
        if tag:
            logger.bind(script=script_name, module=module_name).success(f"[{tag}] {message}")
        else:
            logger.bind(script=script_name, module=module_name).success(f"✓ {message}")

    def warning(self, message, tag=None):
        """警告日志"""
        script_name, module_name = self._get_caller_info()
        if tag:
            logger.bind(script=script_name, module=module_name).warning(f"[{tag}] {message}")
        else:
            logger.bind(script=script_name, module=module_name).warning(f"⚠ {message}")

    def error(self, message, tag=None):
        """错误日志"""
        script_name, module_name = self._get_caller_info()
        if tag:
            logger.bind(script=script_name, module=module_name).error(f"[{tag}] {message}")
        else:
            logger.bind(script=script_name, module=module_name).error(f"✗ {message}")

    def scan_start(self, url, scan_type):
        """扫描开始日志"""
        script_name, module_name = self._get_caller_info()
        logger.bind(script=script_name, module=module_name).info(f"🚀 扫描启动: {url} -> {scan_type}")

    def fingerprint_result(self, url, cms_list):
        """指纹识别结果日志"""
        script_name, module_name = self._get_caller_info()
        if cms_list:
            cms_str = ", ".join(cms_list)
            logger.bind(script=script_name, module=module_name).success(f"🔍 指纹识别: {url} -> {cms_str}")
        else:
            logger.bind(script=script_name, module=module_name).warning(f"🔍 指纹识别: {url} -> 未识别")

    def nuclei_result(self, result_file, has_vulnerabilities=False):
        """Nuclei扫描结果日志"""
        script_name, module_name = self._get_caller_info()
        if has_vulnerabilities:
            logger.bind(script=script_name, module=module_name).success(f"🎯 发现漏洞: {result_file}")
        else:
            logger.bind(script=script_name, module=module_name).info(f"📄 扫描完成: {result_file}")

# 创建全局日志实例
log = ColorLogger("fnuclei")
