#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义异常类
提供更精确的错误处理
"""

class FnucleiException(Exception):
    """基础异常类"""
    pass

class FingerprintException(FnucleiException):
    """指纹识别相关异常"""
    pass

class FingerprintFileNotFound(FingerprintException):
    """指纹文件未找到"""
    def __init__(self, file_path):
        self.file_path = file_path
        super().__init__(f"指纹文件未找到: {file_path}")

class TagsFileNotFound(FnucleiException):
    """标签文件未找到"""
    def __init__(self, file_path):
        self.file_path = file_path
        super().__init__(f"标签文件未找到: {file_path}")

class NetworkException(FnucleiException):
    """网络请求异常"""
    def __init__(self, url, error):
        self.url = url
        self.error = error
        super().__init__(f"网络请求失败 {url}: {error}")

class NucleiException(FnucleiException):
    """Nuclei扫描异常"""
    def __init__(self, command, error):
        self.command = command
        self.error = error
        super().__init__(f"Nuclei扫描失败 [{command}]: {error}")
